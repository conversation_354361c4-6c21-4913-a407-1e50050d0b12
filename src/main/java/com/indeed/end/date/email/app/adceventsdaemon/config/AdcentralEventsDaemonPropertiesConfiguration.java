package com.indeed.end.date.email.app.adceventsdaemon.config;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Nonnull;
import java.io.File;

@Configuration
public class AdcentralEventsDaemonPropertiesConfiguration implements InitializingBean {

    @Value("${bongo.config.dir}")
    private String bongoConfigDir;

    @Nonnull
    public File getBongoConfigDirectory() {
        return new File(bongoConfigDir);
    }

    @Override
    public void afterPropertiesSet() {
        // not implemented
    }
}
