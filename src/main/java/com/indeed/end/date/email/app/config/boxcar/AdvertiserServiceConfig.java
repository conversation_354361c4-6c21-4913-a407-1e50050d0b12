package com.indeed.end.date.email.app.config.boxcar;

import com.indeed.advertiserservice.client.AdvertiserService;
import com.indeed.advertiserservice.client.AdvertiserServiceRpcClient;
import com.indeed.advertiserservice.client.AdvertiserServiceRpcClientFactory;
import com.indeed.boxcar.client.BoxcarTransportSettings;
import com.indeed.common.base.IndeedSystemProperty;
import com.indeed.end.date.email.app.adceventsdaemon.config.AdcentralEventsDaemonPropertiesConfiguration;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.end.date.email.app.util.AdvertiserUtil;
import com.indeed.hystrix.proxy.HystrixProxyBuilder;
import com.indeed.status.core.Urgency;
import com.indeed.util.core.time.WallClock;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class AdvertiserServiceConfig {

    private final AdcentralEventsDaemonPropertiesConfiguration propertiesConfiguration;
    private final ApplicationContext applicationContext;

    @Bean(destroyMethod = "shutdown")
    public AdvertiserService advertiserService() {
        final AdvertiserServiceRpcClient advertiserServiceRpcClient =
                advertiserServiceRpcClientFactory().get();
        return new HystrixProxyBuilder<>(advertiserServiceRpcClient, AdvertiserService.class)
                .setHcUrgency(Urgency.REQUIRED)
                .setHcDescription("Advertiser Service is a boxcar service used for looking up advertiser information")
                .setHcDocumentationUrl("https://wiki.indeed.com/display/SMBHiring/Dradis+AdvertiserService+Dependency")
                .setHealthcheckTriggerRate(95)
                .build();
    }

    @Bean(destroyMethod = "shutdown")
    public AdvertiserServiceRpcClientFactory advertiserServiceRpcClientFactory() {
        final AdvertiserServiceRpcClientFactory factory = new AdvertiserServiceRpcClientFactory(
                propertiesConfiguration.getBongoConfigDirectory(), AdvertiserServiceRpcClientFactory.Pool.READ_ONLY);
        factory.setTransportSettings(BoxcarTransportSettings.Defaults.HIGH_LOAD);
        factory.setClientApp(IndeedSystemProperty.APPLICATION.value());
        factory.extractApiKeySupplierFrom(applicationContext);
        return factory;
    }

    @Bean(name = "advertiserUtilLD")
    public AdvertiserUtil advertiserUtil(final MetricsEmitter metricsEmitter, final WallClock wallClock) {
        return new AdvertiserUtil(advertiserService(), metricsEmitter, wallClock);
    }
}
