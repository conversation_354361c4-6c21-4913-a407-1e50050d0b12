package com.indeed.end.date.email.app.config;

import com.indeed.boxcar.transport.util.NamedThreadFactory;
import com.indeed.end.date.email.app.groups.EndDateEmailAppGroupsManager;
import com.indeed.proctor.common.AbstractProctorLoader;
import com.indeed.proctor.common.JsonProctorLoaderFactory;
import com.indeed.proctor.internal.IndeedJsonProctorLoaderFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

@Configuration
public class ProctorConfig {
    private static final int PROCTOR_REFRESH_RATE_SECONDS = 30;

    @Bean
    AbstractProctorLoader proctorSupplier() {
        final JsonProctorLoaderFactory proctorLoaderFactory = new IndeedJsonProctorLoaderFactoryBean();
        // same path as in the build.gradle
        proctorLoaderFactory.setSpecificationResource(
                "classpath:/proctor/end-date-email-app-proctor-specification.json");
        return proctorLoaderFactory.getLoader();
    }

    @Bean
    EndDateEmailAppGroupsManager groupsManager(final AbstractProctorLoader proctorSupplier) {
        return new EndDateEmailAppGroupsManager(proctorSupplier);
    }

    @Bean
    ScheduledExecutorService scheduledExecutorService(final AbstractProctorLoader proctorSupplier) {
        final ThreadFactory threadFactory = new NamedThreadFactory("EndDateEmailProctorLoaderTasks");
        final ScheduledThreadPoolExecutor scheduledThreadPoolExecutor =
                new ScheduledThreadPoolExecutor(4, threadFactory);
        scheduledThreadPoolExecutor.scheduleAtFixedRate(
                proctorSupplier, 0, PROCTOR_REFRESH_RATE_SECONDS, TimeUnit.SECONDS);
        return scheduledThreadPoolExecutor;
    }
}
