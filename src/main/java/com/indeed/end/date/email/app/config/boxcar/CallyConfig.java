package com.indeed.end.date.email.app.config.boxcar;

import com.indeed.boxcar.client.BoxcarTransportSettings;
import com.indeed.cally.client.CallyRpcClient;
import com.indeed.cally.client.CallyRpcClientFactory;
import com.indeed.cally.client.CallyService;
import com.indeed.common.base.IndeedSystemProperty;
import com.indeed.end.date.email.app.adceventsdaemon.config.AdcentralEventsDaemonPropertiesConfiguration;
import com.indeed.hystrix.proxy.HystrixProxyBuilder;
import com.indeed.status.core.Urgency;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@AllArgsConstructor
public class CallyConfig {

    private final AdcentralEventsDaemonPropertiesConfiguration propertiesConfiguration;

    @Bean
    public CallyService callyService() {
        final CallyRpcClient client = callyRpcClientFactory().get();
        return new HystrixProxyBuilder<>(client, CallyService.class)
                .setHcUrgency(Urgency.REQUIRED)
                .setHcDescription("Cally Service")
                .setHcDocumentationUrl("https://wiki.indeed.com/display/DRADIS/Cally")
                .setHealthcheckTriggerRate(95)
                .build();
    }

    @Bean(destroyMethod = "shutdown")
    public CallyRpcClientFactory callyRpcClientFactory() {
        final CallyRpcClientFactory factory = new CallyRpcClientFactory();
        factory.setBongoConfigDirectory(propertiesConfiguration.getBongoConfigDirectory());
        factory.setClientApp(IndeedSystemProperty.APPLICATION.value());
        factory.setTransportSettings(BoxcarTransportSettings.Defaults.STANDARD);
        return factory;
    }
}
