package com.indeed.end.date.email.app.model;

import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;

@Builder
@Getter
public class EndDateEmailJobData {
    private int advertiserId;
    private int atsJobId;
    private boolean freeJob;
    private String title;
    private String company;
    private Date endDate;
    private List<String> advertisingLocations;
}
