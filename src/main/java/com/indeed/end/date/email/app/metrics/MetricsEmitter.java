package com.indeed.end.date.email.app.metrics;

import com.google.common.collect.ImmutableList;
import com.indeed.end.date.email.app.enums.ErrorCause;
import com.indeed.end.date.email.app.enums.ServiceName;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import org.springframework.stereotype.Component;

import java.time.Clock;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_EMAIL_SENT;
import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_SERVICE_FAILURE;
import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_SERVICE_SUCCESS;
import static com.indeed.end.date.email.app.enums.ServiceName.END_DATE_EMAIL;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SEND_ERROR_CAUSE;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SEND_SUCCESS;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SERVICE_NAME;

@Component
public class MetricsEmitter {

    private static final String PREFIX = "end_date_email";
    private static final String EXTERNAL_PROCESS_TIME = PREFIX + ".call.process.time";

    private final MeterRegistry meterRegistry;
    private final Clock clock;

    public MetricsEmitter(final MeterRegistry meterRegistry, final Clock clock) {
        this.meterRegistry = meterRegistry;
        this.clock = clock;
    }

    public void emitEndDateEmailSent() {
        final List<Tag> tags = ImmutableList.of(
                Tag.of(TAG_SERVICE_NAME, END_DATE_EMAIL.name()),
                Tag.of(TAG_SEND_SUCCESS, Boolean.toString(Boolean.TRUE)));
        meterRegistry.counter(END_DATE_EMAIL_SENT, tags).increment();
    }

    public void emitEndDateEmailFailed(final ErrorCause errorCause) {
        final List<Tag> tags = ImmutableList.of(
                Tag.of(TAG_SERVICE_NAME, END_DATE_EMAIL.name()),
                Tag.of(TAG_SEND_SUCCESS, Boolean.toString(Boolean.FALSE)),
                Tag.of(TAG_SEND_ERROR_CAUSE, errorCause.name()));

        meterRegistry.counter(END_DATE_EMAIL_SENT, tags).increment();
    }

    public void emitEndDateEmailRequeued(final ServiceName service) {
        final List<Tag> tags = ImmutableList.of(Tag.of(TAG_SERVICE_NAME, service.name()));

        meterRegistry.counter(END_DATE_SERVICE_FAILURE, tags).increment();
    }

    public void registerExternalServiceCall(final ServiceName service, final long startTimeMs) {
        final Iterable<Tag> tags = ImmutableList.of(
                Tag.of(TAG_SERVICE_NAME, service.name()), Tag.of(END_DATE_SERVICE_SUCCESS, Boolean.toString(true)));

        meterRegistry.timer(EXTERNAL_PROCESS_TIME, tags).record(clock.millis() - startTimeMs, TimeUnit.MILLISECONDS);
    }
}
