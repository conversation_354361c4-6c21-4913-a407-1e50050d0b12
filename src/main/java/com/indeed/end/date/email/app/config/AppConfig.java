package com.indeed.end.date.email.app.config;

import com.google.common.collect.ImmutableList;
import com.indeed.cally.client.CallyService;
import com.indeed.common.i18n.spring.config.CommonI18nConfig;
import com.indeed.dradis.common.config.DradisBoxcarDefaults;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.end.date.email.app.util.EmailUtil;
import com.indeed.end.date.email.app.util.JobUtil;
import com.indeed.end.date.email.app.util.LogEntryUtil;
import com.indeed.monetization.platform.library.core.adtrafficstats.GetDailyAdTrafficStats;
import com.indeed.raven.delivery.client.RavenDeliveryClient;
import com.indeed.util.core.time.WallClock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.File;

/**
 * Note: if you decide to @ComponentScan, do NOT include {@link MvcWebConfig} !!!
 */
@Configuration
@ComponentScan(basePackages = {"com.indeed.common.money", "io.micrometer.core"})
@Import({CommonI18nConfig.class})
public class AppConfig {

    @Value("${bongo.config.dir}")
    private File bongoConfigDir;

    @Bean
    public LogEntryUtil logEntryUtil() {
        return new LogEntryUtil();
    }

    @Bean
    public JobUtil jobUtil(
            final GetDailyAdTrafficStats getDailyAdTrafficStats,
            final CallyService callyService,
            final WallClock wallClock,
            final MetricsEmitter metricsEmitter) {
        return new JobUtil(getDailyAdTrafficStats, callyService, wallClock, metricsEmitter);
    }

    @Bean
    public EmailUtil emailUtil(
            final RavenDeliveryClient ravenDeliveryClient, MetricsEmitter metricsEmitter, final WallClock wallClock) {
        return new EmailUtil(ravenDeliveryClient, metricsEmitter, wallClock);
    }

    @Bean
    public DradisBoxcarDefaults dradisBoxcarDefaults() {
        return new DradisBoxcarDefaults(ImmutableList.of(), bongoConfigDir);
    }
}
