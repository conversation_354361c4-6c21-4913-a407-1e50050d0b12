package com.indeed.end.date.email.app.config;

import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import com.indeed.adcentral.proto.common.AdCentralRabbitConstants;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage.ActivityType;
import com.indeed.common.message.MessageConsumer;
import com.indeed.common.message.MessageProducer;
import com.indeed.common.message.rabbitmq.MessageConsumerBuilder;
import com.indeed.end.date.email.app.handler.AdCentralEventCallback;
import com.indeed.end.date.email.app.handler.EventProducerMessageParser;
import com.indeed.end.date.email.app.handler.MemcachedDependency;
import com.indeed.mopsy.registry.ConsumerRegistry;
import com.indeed.status.core.Urgency;
import net.spy.memcached.AddrUtil;
import net.spy.memcached.MemcachedClient;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.indeed.common.message.rabbitmq.util.MessageHelper.createMessageConsumerBuilderFromConfiguration;

@Configuration
public class AdcentralRabbitConfig {
    private static final String ADCENTRAL_EVENT_QUEUE_NAME = "end-date-events";
    List<String> TOPICS = Lists.transform(
            Lists.newArrayList(ActivityType.PAUSE_JOB, ActivityType.SEND_END_DATE_REMINDER),
            Functions.toStringFunction());
    private static final int SCHEDULED_EXECUTOR_NUM_THREADS = 1;
    private static final int ADCENTRAL_DELAY_QUEUE_TTL = 30;
    private static final int DEFAULT_DEPENDENCY_TIMEOUT_MS = 5000;

    @Value("${adcentral.rabbit.config.file}")
    private String adCentralRabbitConfigFile;

    @Value("${memcached.host}")
    private String memcachedHost;

    @Bean
    public MessageConsumerBuilder adcentralEventsConsumerBuilder(final ExecutorService adcentralEventsConsumerExecutor)
            throws ConfigurationException {
        return createMessageConsumerBuilderFromConfiguration(new PropertiesConfiguration(adCentralRabbitConfigFile))
                .withDeadLetter(true)
                .withExchangeName(AdCentralRabbitConstants.EVENTS_EXCHANGE_NAME)
                .withRecoverDeadLetterExchange(true)
                .withRecoverDeadLetterQueue(true)
                .withQueueName(ADCENTRAL_EVENT_QUEUE_NAME)
                .withBindingKeys(TOPICS)
                .withWorkerExecutor(adcentralEventsConsumerExecutor);
    }

    @Bean(initMethod = "attach", destroyMethod = "shutdown")
    MessageProducer requeueProducer(final MessageConsumerBuilder adcentralEventsConsumerBuilder) {
        return adcentralEventsConsumerBuilder
                .createDelayedMessageProducerBuilder(ADCENTRAL_DELAY_QUEUE_TTL, TimeUnit.SECONDS)
                .build();
    }

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Bean(initMethod = "attach", destroyMethod = "detach")
    public MessageConsumer adcentralEventsConsumer(
            final MessageConsumerBuilder adcentralEventsConsumerBuilder,
            final AdCentralEventCallback adCentralEventCallback,
            final ConsumerRegistry consumerRegistry) {
        consumerRegistry.registerConsumer(adcentralEventsConsumerBuilder);
        final MessageConsumer adcentralEventsConsumer = adcentralEventsConsumerBuilder.build();
        adcentralEventsConsumer.setMessageListener(adCentralEventCallback);

        return adcentralEventsConsumer;
    }

    @Bean(destroyMethod = "shutdownNow")
    public ExecutorService adcentralEventsConsumerExecutor() throws Exception {
        final ScheduledExecutorService executorService =
                Executors.newScheduledThreadPool(SCHEDULED_EXECUTOR_NUM_THREADS, Executors.defaultThreadFactory());
        return executorService;
    }

    @Bean
    public MemcachedClient memcachedClient() throws IOException {
        return new MemcachedClient(AddrUtil.getAddresses(memcachedHost));
    }

    @Bean
    public MemcachedDependency memcachedDependency() throws IOException {
        return new MemcachedDependency(memcachedClient(), Urgency.STRONG, DEFAULT_DEPENDENCY_TIMEOUT_MS);
    }

    @Bean()
    public EventProducerMessageParser eventProducerMessageParser() {
        return new EventProducerMessageParser();
    }

    @Bean
    public ConsumerRegistry consumerRegistry() {
        return new ConsumerRegistry();
    }
}
