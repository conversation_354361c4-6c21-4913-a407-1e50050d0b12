package com.indeed.end.date.email.app.adceventsdaemon.handler;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage.ActivityType;
import com.indeed.advertiserservice.client.AdvertiserServiceRpcClientException;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.Advertiser;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.AdvertiserProperty;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.PropertyName;
import com.indeed.common.base.IndeedStagingLevel;
import com.indeed.common.message.ConsumerResponse;
import com.indeed.common.money.currency.LocalBillableCurrencyAmountFactory;
import com.indeed.common.money.currency.LocalCurrencyAmount;
import com.indeed.common.money.util.LocalCurrencyAmountFormat;
import com.indeed.common.util.StringUtils;
import com.indeed.dradis.common.encryption.EncryptionUtil;
import com.indeed.dradis.common.encryption.EncryptionUtil.EncryptionType;
import com.indeed.dradis.common.proto.JobProtos.DailyTrafficStats;
import com.indeed.dradis.common.util.DradisJobUtils;
import com.indeed.dradis.common.util.SuperSmartDateFormatter;
import com.indeed.end.date.email.app.enums.ErrorCause;
import com.indeed.end.date.email.app.enums.ServiceName;
import com.indeed.end.date.email.app.logging.LogEntryKeys;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.end.date.email.app.model.EndDateEmailJobData;
import com.indeed.end.date.email.app.model.RavenEmail;
import com.indeed.end.date.email.app.onegraph.util.HostedSponsoredJobsService;
import com.indeed.end.date.email.app.util.AdvertiserUtil;
import com.indeed.end.date.email.app.util.EmailUtil;
import com.indeed.end.date.email.app.util.JobUtil;
import com.indeed.end.date.email.app.util.LogEntryUtil;
import com.indeed.end.date.email.app.util.ProctorUtil;
import com.indeed.raven.delivery.client.RavenDeliveryRpcClientException;
import com.indeed.raven.delivery.rpc.Status;
import com.indeed.util.core.time.WallClock;
import com.indeed.util.logging.Loggers;
import jakarta.annotation.Nonnull;
import org.apache.commons.lang3.LocaleUtils;
import org.apache.log4j.Logger;
import org.joda.money.CurrencyUnit;
import org.joda.time.DateTimeUtils;
import org.jooq.tools.json.JSONObject;
import org.jooq.tools.json.JSONValue;
import org.jooq.tools.json.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;

@Component
public class EndDateEmailHandler implements AdCentralEventHandler {

    private static final Logger LOGGER = Logger.getLogger(EndDateEmailHandler.class);
    private static final String DEFAULT_TIME_ZONE = "GMT";
    private static final String ATS_JOB_ID_KEY = "ats_job_id";
    private static final String PAUSE_REASON_KEY = "updateStatusReason";
    private static final String END_DATE_EMAIL_UPDATE_REASON_KEY = "SPONSORED_JOB_END_DATE_REACHED";
    private static final String DAYS_EARLY_KEY = "days_early";

    @VisibleForTesting
    static final String REJECTED = "rejected";

    static final String JOB_TITLE_KEY = "jobTitle";
    static final String JOB_LOCATION_KEY = "jobLocation";
    static final String COMPANY_KEY = "company";
    static final String CANDIDATE_COUNT_KEY = "displayCandidateCount";
    static final String NON_SPONSORED_JOB_APPS_KEY = "nonSponsoredJobApps";
    static final String END_DATE_KEY = "displayEndDate";
    static final String DISPLAY_TOTAL_COST_KEY = "displayTotalCost";
    static final String RAVEN_DAYS_EARLY_KEY = "daysEarly";
    static final String ENCRYPTED_JOB_ID_KEY = "encryptedJobId";
    static final String RAVEN_DEDUPLICATION_ID = "jobHashDate";

    static final String JOB_HASH = "jobHash";
    static final String RAVEN_CAMPAIGN_NAME_EN_US = "retention_enddate_ptp_en_us";
    static final String RAVEN_CAMPAIGN_NAME_INTL = "retention_enddate_ptp_intl";

    private final LogEntryUtil logEntryUtil;
    private final AdvertiserUtil advertiserUtil;
    private final EmailUtil emailUtil;
    private final JobUtil jobUtil;
    private final WallClock wallClock;
    private final ProctorUtil proctorUtil;
    private final MetricsEmitter metricsEmitter;

    private final HostedSponsoredJobsService hostedSponsoredJobsService;

    @Autowired
    EndDateEmailHandler(
            final LogEntryUtil logEntryUtil,
            @Qualifier("advertiserUtilLD") final AdvertiserUtil advertiserUtil,
            final EmailUtil emailUtil,
            final JobUtil jobUtil,
            final WallClock wallClock,
            final MetricsEmitter metricsEmitter,
            final HostedSponsoredJobsService hostedSponsoredJobsService,
            final ProctorUtil proctorUtil) {
        this.logEntryUtil = logEntryUtil;
        this.advertiserUtil = advertiserUtil;
        this.emailUtil = emailUtil;
        this.jobUtil = jobUtil;
        this.wallClock = wallClock;
        this.metricsEmitter = metricsEmitter;
        this.hostedSponsoredJobsService = hostedSponsoredJobsService;
        this.proctorUtil = proctorUtil;
    }

    @Override
    public ConsumerResponse handleEvent(@Nonnull final EventProducerMessage event) {
        final long startTime = wallClock.currentTimeMillis();
        final int advertiserId = event.getAdvertiserId();
        final ActivityType activityType = event.getActivityType();
        final String eventName = activityType.toString();

        if (advertiserId < 1) {
            Loggers.warn(LOGGER, "Advertiser id [%d] is not valid for event %s", advertiserId, eventName);
            metricsEmitter.emitEndDateEmailFailed(ErrorCause.INVALID_MESSAGE);
            return ConsumerResponse.ACK;
        }

        // sample PAUSE_JOB json:
        // {"ats_job_id":1,"encrypted_job_id":"e88c291983d8","ip_country":"","ip_address":"","diffs":[],"send-email":1,"suppressExternal":true}
        // sample SEND_END_DATE_REMINDER json: {"ats_job_id":1,"days_early":3,"suppressExternal":true}
        final JSONObject json = parseJson(event.getJsonString(), eventName, advertiserId);

        if (json == null || !isValidJson(json, activityType, advertiserId)) {
            metricsEmitter.emitEndDateEmailFailed(ErrorCause.INVALID_MESSAGE);
            return ConsumerResponse.ACK;
        }

        final Advertiser advertiser;
        try {
            final Optional<Advertiser> advertiserOptional =
                    advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(advertiserId);
            if (advertiserOptional.isPresent()) {
                advertiser = advertiserOptional.get();
            } else {
                Loggers.error(
                        LOGGER,
                        "Unable to get Advertiser for advertiserId [%d] but no exception was thrown. Nacking",
                        advertiserId);
                metricsEmitter.emitEndDateEmailFailed(ErrorCause.ADVERTISER_ERROR);
                return ConsumerResponse.NACK;
            }
        } catch (final AdvertiserServiceRpcClientException e) {
            Loggers.error(
                    LOGGER, "Failed to get Advertiser for advertiserId [%d] due to service exception", e, advertiserId);
            metricsEmitter.emitEndDateEmailRequeued(ServiceName.ADVERTISER_SERVICE);
            return ConsumerResponse.NACK_WITH_REQUEUE;
        }

        final List<AdvertiserProperty> advertiserProperties;
        try {
            advertiserProperties = advertiserUtil.getAdvertiserPropertiesUnsafe(
                    advertiserId, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS));
        } catch (final AdvertiserServiceRpcClientException e) {
            Loggers.error(LOGGER, "Unable to get AdvertiserProperties for advertiserId [%d]", e, advertiserId);
            metricsEmitter.emitEndDateEmailRequeued(ServiceName.ADVERTISER_SERVICE);
            return ConsumerResponse.NACK_WITH_REQUEUE;
        }

        final Integer atsJobId = Math.toIntExact((Long) json.get(ATS_JOB_ID_KEY));
        final ImmutableMap.Builder<String, String> loggingParamsBuilder = new ImmutableMap.Builder<>();
        loggingParamsBuilder.put(LogEntryKeys.ADVERTISER_ID_KEY, String.valueOf(advertiserId));
        loggingParamsBuilder.put(LogEntryKeys.ATS_JOB_ID_KEY, atsJobId.toString());
        loggingParamsBuilder.put(LogEntryKeys.ACTIVITY_TYPE_KEY, eventName);
        loggingParamsBuilder.put(LogEntryKeys.SEND_RAVEN_EMAIL, "1");

        if (isRejected(advertiserId, advertiserProperties)) {
            Loggers.warn(LOGGER, "advertiserId [%d] is rejected. skipping end date email", advertiserId);
            metricsEmitter.emitEndDateEmailFailed(ErrorCause.ADVERTISER_REJECTED);
            return ConsumerResponse.ACK;
        }

        final EndDateEmailJobData job;
        try {
            job = hostedSponsoredJobsService.getJob(advertiserId, atsJobId);

            if (job == null) {
                Loggers.error(
                        LOGGER,
                        "No Job returned for atsJobId [%d] and advertiserId [%d], but no exception was thrown. Nacking",
                        atsJobId,
                        advertiserId);
                metricsEmitter.emitEndDateEmailFailed(ErrorCause.MISSING_JOB);
                return ConsumerResponse.NACK;
            }
        } catch (final Exception e) {
            Loggers.error(
                    LOGGER,
                    "Unable to retrieve Job for advertiserId [%d], atsJobId [%d] due to exception. Requeueing",
                    e,
                    advertiserId,
                    atsJobId);
            metricsEmitter.emitEndDateEmailRequeued(ServiceName.ONE_GRAPH);
            return ConsumerResponse.NACK_WITH_REQUEUE;
        }

        if (job.isFreeJob()) {
            Loggers.info(
                    LOGGER,
                    "Email was filtered for advertiserId [%d], atsJobId [%d] because is a free job",
                    advertiserId,
                    atsJobId);
            metricsEmitter.emitEndDateEmailFailed(ErrorCause.FREE_JOB_FILTERED);
            return ConsumerResponse.ACK;
        }

        if (job.getTitle() == null
                || job.getAdvertisingLocations() == null
                || job.getAdvertisingLocations().size() == 0) {
            Loggers.warn(
                    LOGGER,
                    "Job with atsJobId [%d] for advertiserId [%d] is missing title or location!",
                    atsJobId,
                    advertiserId);
            metricsEmitter.emitEndDateEmailFailed(ErrorCause.INVALID_JOB);
            return ConsumerResponse.NACK;
        }

        final Optional<Integer> candidateCountOptional;
        final Optional<Integer> nonSponsoredJobAppsOptional;

        try {
            candidateCountOptional = jobUtil.getCandidateCountsForJobUnsafe(advertiserId, atsJobId);

            if (!candidateCountOptional.isPresent()) {
                Loggers.error(
                        LOGGER,
                        "Unable to get candidate counts for advertiserId [%d], atsJobId [%d] but no exception was thrown. Nacking",
                        advertiserId,
                        atsJobId);
                metricsEmitter.emitEndDateEmailFailed(ErrorCause.CANDIDATES_ERROR);
                return ConsumerResponse.NACK;
            }

            nonSponsoredJobAppsOptional = Optional.of((int) Math.round(candidateCountOptional.get() / 1.6));
        } catch (final IOException e) {
            Loggers.error(
                    LOGGER,
                    "Unable to get candidate counts for advertiserId [%d], atsJobId [%d] due to service exception",
                    e,
                    advertiserId,
                    atsJobId);
            metricsEmitter.emitEndDateEmailRequeued(ServiceName.CALLY);
            return ConsumerResponse.NACK_WITH_REQUEUE;
        }

        final String encryptedJobId = EncryptionUtil.safeEncode(atsJobId, advertiserId, EncryptionType.JOB_ID);
        if (StringUtils.isEmpty(encryptedJobId)) {
            Loggers.error(LOGGER, "Unable to encrypt atsJobId [%d] for advertiserId [%d]", atsJobId, advertiserId);
            return ConsumerResponse.NACK_WITH_REQUEUE;
        }

        final Optional<String> languageLocale = getLocaleStringFromProperties(advertiserProperties);
        final Optional<String> billingCountry =
                advertiserUtil.getBillingCountryForAdvertiserId(advertiser.getAdvertiserId());
        final Locale locale = localeForEmail(languageLocale, billingCountry);
        final String ravenCampaign = Locale.US.equals(locale) ? RAVEN_CAMPAIGN_NAME_EN_US : RAVEN_CAMPAIGN_NAME_INTL;
        final Long totalCostLM = getTotalCostLM(advertiserId, atsJobId);
        if (totalCostLM == null) {
            metricsEmitter.emitEndDateEmailRequeued(ServiceName.GET_DAILY_AD_TRAFFIC_STATS);
            // ACK in QA since we expect a lot of fake advertiser ids, which will kill the consumers
            return (IndeedStagingLevel.get() == IndeedStagingLevel.QA)
                    ? ConsumerResponse.ACK
                    : ConsumerResponse.NACK_WITH_REQUEUE;
        }

        if (billingCountry.isPresent()) {
            loggingParamsBuilder.put(LogEntryKeys.BILLING_COUNTRY, billingCountry.get());
        }

        try {
            final long now = wallClock.currentTimeMillis();
            final Map<String, Object> ravenDataMap = ImmutableMap.<String, Object>builder()
                    .put(JOB_TITLE_KEY, job.getTitle())
                    .put(
                            JOB_LOCATION_KEY,
                            DradisJobUtils.getDisplayLocationStringFromStrings(
                                    job.getAdvertisingLocations(), locale.getCountry(), locale, false))
                    .put(COMPANY_KEY, job.getCompany())
                    .put(CANDIDATE_COUNT_KEY, candidateCountOptional.get())
                    .put(NON_SPONSORED_JOB_APPS_KEY, nonSponsoredJobAppsOptional.get())
                    .put(
                            END_DATE_KEY,
                            SuperSmartDateFormatter.formatDate(
                                    now,
                                    job.getEndDate() != null
                                            ? job.getEndDate().getTime()
                                            : DateTimeUtils.currentTimeMillis(),
                                    locale,
                                    TimeZone.getTimeZone(DEFAULT_TIME_ZONE)))
                    .put(
                            DISPLAY_TOTAL_COST_KEY,
                            getFormattedSpend(locale, advertiser.getCurrency().toString(), totalCostLM))
                    .put(
                            RAVEN_DAYS_EARLY_KEY,
                            (activityType == ActivityType.PAUSE_JOB || json.get(DAYS_EARLY_KEY) == null)
                                    ? 0
                                    : (Long) json.get(DAYS_EARLY_KEY))
                    .put(ENCRYPTED_JOB_ID_KEY, encryptedJobId)
                    .put(
                            RAVEN_DEDUPLICATION_ID,
                            String.format("%d_%d_%s", advertiserId, atsJobId, getFormattedDate(now)))
                    .put(JOB_HASH, String.format("%d_%d", advertiserId, atsJobId))
                    .build();

            final Optional<RavenEmail> ravenEmailOptional =
                    emailUtil.sendEmailUnsafe(advertiserId, ravenCampaign, ravenDataMap, locale);

            if (!ravenEmailOptional.isPresent()) {
                Loggers.error(
                        LOGGER,
                        "Raven email not sent for advertiserId [%d], atsJobId [%d], campaign %s. Nacking",
                        advertiserId,
                        atsJobId,
                        ravenCampaign);
                metricsEmitter.emitEndDateEmailFailed(ErrorCause.RAVEN_ERROR);
                return ConsumerResponse.NACK; // something is messed up, so send it to the DLX so we can check it
            }

            final RavenEmail ravenEmail = ravenEmailOptional.get();
            if (ravenEmail.getStatus() != null && ravenEmail.getStatus() == Status.FILTER) {
                Loggers.info(
                        LOGGER,
                        "%s email was filtered for advertiserId [%d], atsJobId [%d]",
                        ravenCampaign,
                        advertiserId,
                        atsJobId);
                metricsEmitter.emitEndDateEmailFailed(ErrorCause.RAVEN_FILTERED);
                return ConsumerResponse.ACK;
            }

            addRavenInfoToLoggingParamMap(loggingParamsBuilder, ravenEmailOptional.get(), ravenCampaign);
        } catch (final RavenDeliveryRpcClientException e) {
            Loggers.error(
                    LOGGER,
                    "Error sending %s email through Raven Delivery for advertiserId [%d]",
                    e,
                    ravenCampaign,
                    advertiserId);
            metricsEmitter.emitEndDateEmailRequeued(ServiceName.RAVEN);
            return ConsumerResponse.NACK_WITH_REQUEUE;
        }

        Loggers.info(
                LOGGER,
                "Sending end date email [campaign=%s, advertiserId=%d, params=%s",
                ravenCampaign,
                advertiserId,
                loggingParamsBuilder.build().toString());

        logEntryUtil.logTypeWithParamMap(LogEntryKeys.END_DATE_EMAIL_LOG_ENTRY, loggingParamsBuilder.build());
        metricsEmitter.emitEndDateEmailSent();
        return ConsumerResponse.ACK;
    }

    private JSONObject parseJson(final String jsonString, final String eventName, final int advertiserId) {
        if (jsonString.length() == 0) {
            Loggers.error(LOGGER, "Json message for advertiserId [%d] is empty; rejecting", advertiserId);
            return null;
        }

        final JSONObject json;
        try {
            json = (JSONObject) JSONValue.parseWithException(jsonString);
        } catch (final ParseException e) {
            Loggers.error(
                    LOGGER,
                    "Could not parse %s json for advertiserId [%d]; message length is [%d]",
                    e,
                    eventName,
                    advertiserId,
                    jsonString.length());

            return null;
        }

        return json;
    }

    @VisibleForTesting
    boolean isValidJson(final JSONObject json, final ActivityType activityType, final int advertiserId) {
        final Long atsJobId = (Long) json.get(ATS_JOB_ID_KEY);
        if (atsJobId == null || atsJobId < 1) {
            Loggers.warn(
                    LOGGER,
                    "Invalid atsJobId [%d] for %s event for advertiserId [%d]",
                    atsJobId,
                    activityType.toString(),
                    advertiserId);
            return false;
        }

        // we only want to process PAUSE_JOB activities that have "updateStatusReason":"SPONSORED_JOB_END_DATE_REACHED"
        // in their json
        if (activityType == ActivityType.PAUSE_JOB) {
            final String updateStatusReason = (String) json.get(PAUSE_REASON_KEY);

            if (!END_DATE_EMAIL_UPDATE_REASON_KEY.equals(updateStatusReason)) {
                Loggers.debug(
                        LOGGER,
                        "Activity %s for advertiserId [%d], atsJobId [%d] did not come from DradisEndDatePauseTool. acking",
                        activityType.toString(),
                        advertiserId,
                        atsJobId);
                return false;
            }
        }

        return true;
    }

    @VisibleForTesting
    Optional<String> getLocaleStringFromProperties(final List<AdvertiserProperty> advertiserProperties) {
        return advertiserProperties.stream()
                .filter(advertiserProperty -> advertiserProperty.hasPropertyName()
                        && PropertyName.LOCALE.equals(advertiserProperty.getPropertyName())
                        && advertiserProperty.hasPropertyValue())
                .map(AdvertiserProperty::getPropertyValue)
                .findFirst();
    }

    @VisibleForTesting
    boolean isRejected(final int advertiserId, final List<AdvertiserProperty> advertiserProperties) {
        final Optional<AdvertiserProperty> showHostedJobsPropertyOptional = advertiserProperties.stream()
                .filter(advertiserProperty ->
                        advertiserProperty.getPropertyName().equals(PropertyName.SHOW_HOSTED_JOBS))
                .findFirst();

        if (showHostedJobsPropertyOptional.isPresent()
                && showHostedJobsPropertyOptional.get().hasPropertyValue()
                && REJECTED.equals(showHostedJobsPropertyOptional.get().getPropertyValue())) {
            Loggers.warn(LOGGER, "advertiserId [%d] is rejected. skipping end date email", advertiserId);
            return true;
        }

        return false;
    }

    @VisibleForTesting
    Long getTotalCostLM(final int advertiserId, final int atsJobId) {
        final Optional<DailyTrafficStats> dailyTrafficStats;
        try {
            dailyTrafficStats = jobUtil.getTrafficStatsForJobUnsafe(advertiserId, atsJobId);
        } catch (final Exception e) {
            Loggers.error(
                    LOGGER,
                    "Failed to get daily traffic stats for advertiserId [%d], atsJobId [%d]",
                    e,
                    advertiserId,
                    atsJobId);
            return null;
        }

        return (dailyTrafficStats.isPresent() && dailyTrafficStats.get().hasCostLocal())
                ? dailyTrafficStats.get().getCostLocal()
                : 0;
    }

    private String getFormattedSpend(final Locale locale, final String currency, final long totalCostLM) {
        final LocalCurrencyAmountFormat formatter = new LocalCurrencyAmountFormat(locale);
        final CurrencyUnit currencyUnit = LocalBillableCurrencyAmountFactory.getSupportedCurrency(currency);
        final LocalCurrencyAmount amount = LocalBillableCurrencyAmountFactory.fromMinorUnits(currencyUnit, totalCostLM);
        return formatter.format(amount);
    }

    @VisibleForTesting
    void addRavenInfoToLoggingParamMap(
            final ImmutableMap.Builder<String, String> loggingParamMapBuilder,
            final RavenEmail ravenEmail,
            final String ravenCampaign) {
        loggingParamMapBuilder.put(LogEntryKeys.RAVEN_CAMPAIGN_NAME, ravenCampaign);

        final Map<String, Object> dataMap = ravenEmail.getDataMap();
        if (dataMap != null) {
            dataMap.forEach((key, value) -> loggingParamMapBuilder.put(key, value.toString()));
        }

        final Status ravenStatus = ravenEmail.getStatus();
        if (ravenStatus != null) {
            loggingParamMapBuilder.put(LogEntryKeys.RAVEN_DELIVERY_STATUS_KEY, ravenStatus.toString());
        }

        final String sessionUid = ravenEmail.getRavenSessionUid();
        if (sessionUid != null) {
            loggingParamMapBuilder.put(LogEntryKeys.RAVEN_TRACKING_KEY, sessionUid);
        }
    }

    @VisibleForTesting
    String getFormattedDate(final long timestamp) {
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT+6"));
        return simpleDateFormat.format(new Date(timestamp));
    }

    @Override
    public void close() {}

    @Override
    public Collection<ActivityType> getRegisteredActivityTypes() {
        return ImmutableList.of(ActivityType.PAUSE_JOB, ActivityType.SEND_END_DATE_REMINDER);
    }

    @VisibleForTesting
    Locale localeForEmail(final Optional<String> languageLocale, final Optional<String> billingCountry) {
        final String language;
        if (!languageLocale.isPresent()) {
            language = "en";
            Loggers.info(LOGGER, "Language locale not present. Defaulting to %s.", language);
        } else if (languageLocale.get().contains("_") || languageLocale.get().contains("-")) {
            // if the languageLocale string contains a dash or underscore we assume it's a well formed and parsable
            // locale
            // once parsed we extract only the language and combine that with their billing country below.
            language = LocaleUtils.toLocale(languageLocale.get()).getLanguage();
        } else {
            // the advertiser locale typically is language-only and cane be either lower of upper-case.
            // Upper-case language codes do not parse properly so we explicitly convert them to lower case for safety.
            language = LocaleUtils.toLocale(languageLocale.get().toLowerCase(Locale.ROOT))
                    .getLanguage();
        }

        // if billing country is not present, default to US
        return new Locale(language, billingCountry.orElse("US"));
    }
}
