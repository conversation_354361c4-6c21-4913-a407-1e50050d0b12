package com.indeed.end.date.email.app.util;

import com.indeed.cally.api.proto.GetCandidateCountsByJobIDRequest;
import com.indeed.cally.api.proto.GetCandidateCountsByJobIDResponse;
import com.indeed.cally.client.CallyService;
import com.indeed.dradis.common.proto.JobProtos.DailyTrafficStats;
import com.indeed.end.date.email.app.enums.ServiceName;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.monetization.platform.library.core.adtrafficstats.GetDailyAdTrafficStats;
import com.indeed.util.core.time.WallClock;
import com.indeed.util.logging.Loggers;
import org.apache.log4j.Logger;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.util.Optional;

public class JobUtil {
    private static final Logger LOG = Logger.getLogger(JobUtil.class);

    private final GetDailyAdTrafficStats getDailyAdTrafficStats;
    private final CallyService callyService;
    private final WallClock wallClock;
    private final MetricsEmitter metricsEmitter;

    public JobUtil(
            @Nonnull final GetDailyAdTrafficStats getDailyAdTrafficStats,
            @Nonnull final CallyService callyService,
            @Nonnull final WallClock wallClock,
            @Nonnull final MetricsEmitter metricsEmitter) {
        this.getDailyAdTrafficStats = getDailyAdTrafficStats;
        this.callyService = callyService;
        this.wallClock = wallClock;
        this.metricsEmitter = metricsEmitter;
    }

    public Optional<Integer> getCandidateCountsForJobUnsafe(final int advertiserId, final int atsJobId)
            throws IOException {
        if (advertiserId < 1 || atsJobId < 1) {
            Loggers.warn(LOG, "Invalid advertiserId [%d] or atsJobId [%d]", advertiserId, atsJobId);
            return Optional.empty();
        }

        final GetCandidateCountsByJobIDRequest request = GetCandidateCountsByJobIDRequest.newBuilder()
                .setAdvertiserId(advertiserId)
                .addAtsJobId(atsJobId)
                .build();

        final long startTimeMs = wallClock.currentTimeMillis();
        final GetCandidateCountsByJobIDResponse response = callyService.getCandidateCountsByJobID(request);
        metricsEmitter.registerExternalServiceCall(ServiceName.CALLY, startTimeMs);

        // the response only includes jobs with > 0 candidates, so if there were no JobIDToCounts, assume count was 0
        if (response.getJobIDtoCountsCount() == 0) {
            return Optional.of(0);
        } else if (response.getJobIDtoCounts(0).hasCandidateCount()) {
            // response.getJobIDtoCountsCount() always >0
            return Optional.of(response.getJobIDtoCounts(0).getCandidateCount());
        }

        return Optional.empty();
    }

    public Optional<DailyTrafficStats> getTrafficStatsForJobUnsafe(final int advertiserId, final int atsJobId)
            throws Exception {
        if (advertiserId < 1 || atsJobId < 1) {
            Loggers.warn(LOG, "Invalid advertiserId [%d] or atsJobId [%d]", advertiserId, atsJobId);
            return Optional.empty();
        }

        // Create parameters for GetDailyAdTrafficStats
        GetDailyAdTrafficStats.Parameters parameters = new GetDailyAdTrafficStats.Parameters(
                advertiserId,
                atsJobId, // atsJobId not set, so all jobs
                0,
                wallClock.currentTimeMillis(),
                true, // includeDeleted
                false // groupByJob
                );

        final long startTimeMs = wallClock.currentTimeMillis();
        final GetDailyAdTrafficStats.Result result = getDailyAdTrafficStats.getDailyTrafficStats(parameters);
        metricsEmitter.registerExternalServiceCall(ServiceName.GET_DAILY_AD_TRAFFIC_STATS, startTimeMs);

        if (result.totals == null || result.totals.isEmpty()) {
            return result.stats.isEmpty() ? Optional.empty() : Optional.of(result.stats.get(0));
        }

        return Optional.of(result.totals.get(0));
    }
}
