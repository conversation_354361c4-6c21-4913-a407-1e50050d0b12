package com.indeed.end.date.email.app.adceventsdaemon.handler;

import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage.ActivityType;
import com.indeed.common.message.ConsumerResponse;
import jakarta.annotation.Nonnull;

import java.util.Collection;

public interface AdCentralEventHandler {

    ConsumerResponse handleEvent(@Nonnull final EventProducerMessage event);

    void close();

    /**
     * @return a Collection of ActivityTypes this handler will listen too.
     */
    Collection<ActivityType> getRegisteredActivityTypes();
}
