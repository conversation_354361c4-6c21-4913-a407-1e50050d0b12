package com.indeed.end.date.email.app.handler;

import jakarta.annotation.Nonnull;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.spy.memcached.MemcachedClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@Component
@AllArgsConstructor
@Slf4j
public class EventUIDCache {

    private static final String PROCESSING_KEYSPACE = "end-date-email-adcentral-events-daemon:processing:";

    private static final int PROCESSING_TTL = 300; // 5 minutes

    private static final String PROCESSED_KEYSPACE = "end-date-email-adcentral-events-daemon:processed:";

    private static final int PROCESSED_TTL = 86400; // 24 hours

    private final MemcachedClient memcachedClient;

    public enum AddCacheStatus {
        ALREADY_IN_CACHE, // UID was already in cache
        ADDED_TO_CACHE, // UID was added to cache
        ERROR // Error adding UID to cache
    }

    public enum GetCacheStatus {
        IN_CACHE,
        NOT_IN_CACHE,
        ERROR
    }

    /**
     *
     * @param uid - the uid of the event
     * @return
     * @throws InterruptedException
     * @throws java.util.concurrent.ExecutionException
     */
    public AddCacheStatus addUIDToCache(@Nonnull final String key, @Nonnull final String uid, final int ttl)
            throws InterruptedException, ExecutionException {
        final Future<Boolean> resultFuture = memcachedClient.add(key, ttl, uid);
        return resultFuture.get() ? AddCacheStatus.ADDED_TO_CACHE : AddCacheStatus.ALREADY_IN_CACHE;
    }

    /**
     *
     * @param uid - the uid of event.
     * @return
     */
    public AddCacheStatus safeAddUIDToProcessingCache(@Nonnull final String uid) {
        try {
            return addUIDToCache(getProcessingKey(uid), uid, PROCESSING_TTL);
        } catch (Exception e) {
            log.error("Exception thrown when adding key to memchached for UID = " + uid, e);
            return AddCacheStatus.ERROR;
        }
    }

    /**
     *
     * @param uid - the uid of event.
     * @return
     */
    public AddCacheStatus safeAddUIDToProcessedCache(@Nonnull final String uid) {
        try {
            return addUIDToCache(getProcessedKey(uid), uid, PROCESSED_TTL);
        } catch (Exception e) {
            log.error("Exception thrown when adding key to memchached for UID = " + uid, e);
            return AddCacheStatus.ERROR;
        }
    }

    /**
     *
     * @param uid - the uid of the event
     * @return
     */
    public GetCacheStatus isUIDInProcessedCache(@Nonnull final String uid) {
        try {
            final Object obj = memcachedClient.get(getProcessedKey(uid));
            return obj == null ? GetCacheStatus.NOT_IN_CACHE : GetCacheStatus.IN_CACHE;
        } catch (Exception e) {
            log.error("Exception thrown when retrieving uid from processed cache.", e);
            return GetCacheStatus.ERROR;
        }
    }

    private static String getProcessingKey(@Nonnull final String uid) {
        return PROCESSING_KEYSPACE + uid;
    }

    private static String getProcessedKey(@Nonnull final String uid) {
        return PROCESSED_KEYSPACE + uid;
    }

    /**
     *
     * @param uid - the uid of the event to be removed from the processing cache.
     */
    public void safeRemoveFromProcessingCache(@Nonnull final String uid) {
        try {
            memcachedClient.delete(getProcessingKey(uid));
        } catch (Exception e) {
            log.error("Failed to delete UID from cache.", e);
        }
    }
}
