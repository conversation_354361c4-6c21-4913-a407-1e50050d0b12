package com.indeed.end.date.email.app.util;

import com.google.common.collect.ImmutableMap;
import com.indeed.end.date.email.app.groups.EndDateEmailAppGroupsManager;
import com.indeed.end.date.email.app.groups.EndDateEmailAppProctorGroups;
import com.indeed.proctor.common.Identifiers;
import com.indeed.proctor.common.ProctorResult;
import com.indeed.proctor.common.model.TestType;
import com.indeed.proctor.internal.TestTypes;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.LocaleUtils;
import org.springframework.stereotype.Service;

/*
 * Utility for determining the proctor groups for a given advertiser.
 */

@Service
@AllArgsConstructor
@Slf4j
public class ProctorUtil {
    private final EndDateEmailAppGroupsManager proctorGroupsManager;

    public EndDateEmailAppProctorGroups getProctorGroups(
            final int advertiserId, final String hl, final String co, final String claimCode) {

        final ImmutableMap.Builder<TestType, String> identifierValues = ImmutableMap.builder();
        identifierValues.put(TestTypes.AUTHENTICATED_USER, String.valueOf(advertiserId));
        identifierValues.put(TestTypes.ADVERTISER, String.valueOf(advertiserId));
        final Identifiers identifiers = new Identifiers(identifierValues.build(), true);
        ProctorResult proctorResult = ProctorResult.EMPTY;

        try {
            proctorResult = proctorGroupsManager.determineBuckets(
                    identifiers,
                    advertiserId,
                    hl,
                    hl != null ? (LocaleUtils.toLocale(hl).getLanguage()) : null,
                    co,
                    co,
                    claimCode);
        } catch (final Exception e) {
            log.error("Error determining Proctor test group buckets, using fallback values", e);
        }

        return new EndDateEmailAppProctorGroups(proctorResult);
    }

    public EndDateEmailAppProctorGroups getProctorGroups(final int advertiserId, final String hl, final String co) {
        return getProctorGroups(advertiserId, hl, co, "");
    }
}
