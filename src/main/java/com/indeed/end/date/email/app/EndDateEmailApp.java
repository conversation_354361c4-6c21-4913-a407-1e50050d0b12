package com.indeed.end.date.email.app;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = MongoAutoConfiguration.class)
@EnableScheduling
public class EndDateEmailApp {
    public static void main(final String[] args) {
        SpringApplication.run(EndDateEmailApp.class, args);
    }
}
