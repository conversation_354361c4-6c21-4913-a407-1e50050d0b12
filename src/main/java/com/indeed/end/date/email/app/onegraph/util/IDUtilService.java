package com.indeed.end.date.email.app.onegraph.util;

import com.indeed.dradis.common.encryption.EncryptionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@AllArgsConstructor
@Slf4j
public class IDUtilService {
    public String atsJobIdToLegacyJobId(final int atsJobId, final int advertiserId) {
        return EncryptionUtil.safeEncode(atsJobId, advertiserId, EncryptionUtil.EncryptionType.JOB_ID);
    }

    public String getUUID() {
        return UUID.randomUUID().toString();
    }
}
