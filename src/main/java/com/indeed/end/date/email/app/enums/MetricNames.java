package com.indeed.end.date.email.app.enums;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MetricNames {

    private static final String END_DATE_METRICS_PREFIX = "end.date.emailhandler.";

    public static final String END_DATE_EMAIL_SENT = END_DATE_METRICS_PREFIX + "raven.email.sent";

    public static final String END_DATE_SERVICE_FAILURE = END_DATE_METRICS_PREFIX + "emailsend.service.failure";

    public static final String END_DATE_SERVICE_SUCCESS = END_DATE_METRICS_PREFIX + "emailsend.service.success";
}
