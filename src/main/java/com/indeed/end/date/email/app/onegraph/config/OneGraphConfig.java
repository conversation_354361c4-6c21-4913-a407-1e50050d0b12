package com.indeed.end.date.email.app.onegraph.config;

import com.apollographql.apollo3.ApolloClient;
import com.apollographql.apollo3.network.http.DefaultHttpEngine;
import com.indeed.end.date.email.app.client.OneGraphHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class OneGraphConfig {

    /**
     * Creates an Apollo client for making GraphQL requests to OneGraph.
     *
     * @param baseUrl             OneGraph base URL
     * @return Apollo client for making GraphQL requests to OneGraph
     */
    @Bean
    public ApolloClient.Builder oneGraphApolloClientBuilder(
            @Value("${indeed.graphql.client.onegraph.url}") final String baseUrl,
            final OneGraphHttpClient oneGraphHttpClient) {
        return new ApolloClient.Builder()
                .serverUrl(baseUrl + "/graphql")
                .httpEngine(new DefaultHttpEngine(oneGraphHttpClient.getHttpClient()));
    }

    @Bean(destroyMethod = "dispose")
    OneGraphHttpClient oneGraphHttpClient() {
        return new OneGraphHttpClient();
    }
}
