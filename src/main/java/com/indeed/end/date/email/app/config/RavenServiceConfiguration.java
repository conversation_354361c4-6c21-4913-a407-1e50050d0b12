package com.indeed.end.date.email.app.config;

import com.indeed.end.date.email.app.adceventsdaemon.config.AdcentralEventsDaemonPropertiesConfiguration;
import com.indeed.grpc.IndeedDiscovery;
import com.indeed.logging.client.stats.StatsEmitter;
import com.indeed.raven.delivery.client.RavenDeliveryClient;
import com.indeed.raven.delivery.client.RavenDeliveryRpcClientFactory;
import com.indeed.raven.delivery.common.logging.RavenRunCampaignLogUtils;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan(basePackages = {"com.indeed.raven.delivery.common.logging"})
@AllArgsConstructor
public class RavenServiceConfiguration {

    private final AdcentralEventsDaemonPropertiesConfiguration propertiesConfiguration;

    @Bean
    protected RavenDeliveryRpcClientFactory getRavenDeliveryRpcClientFactory(final StatsEmitter statsEmitter) {
        final RavenDeliveryRpcClientFactory ravenDeliveryRpcClientFactory =
                new RavenDeliveryRpcClientFactory(propertiesConfiguration.getBongoConfigDirectory(), statsEmitter);
        ravenDeliveryRpcClientFactory.setDiscovery(IndeedDiscovery.ENVOY_MESH);
        return ravenDeliveryRpcClientFactory;
    }

    @Bean
    public RavenDeliveryClient getRavenDeliveryClient(
            final RavenDeliveryRpcClientFactory ravenDeliveryRpcClientFactory,
            final RavenRunCampaignLogUtils ravenRunCampaignLogUtils) {
        return new RavenDeliveryClient(ravenDeliveryRpcClientFactory, ravenRunCampaignLogUtils);
    }
}
