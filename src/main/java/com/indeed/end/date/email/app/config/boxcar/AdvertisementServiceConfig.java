package com.indeed.end.date.email.app.config.boxcar;

import com.indeed.advertisementservice.client.AdvertisementService;
import com.indeed.advertisementservice.client.AdvertisementServiceRpcClientFactory;
import com.indeed.common.base.IndeedSystemProperty;
import com.indeed.end.date.email.app.adceventsdaemon.config.AdcentralEventsDaemonPropertiesConfiguration;
import com.indeed.grpc.IndeedDiscovery;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@AllArgsConstructor
public class AdvertisementServiceConfig {

    private final AdcentralEventsDaemonPropertiesConfiguration propertiesConfiguration;
    private final ApplicationContext context;

    @Bean(destroyMethod = "shutdown")
    AdvertisementServiceRpcClientFactory advertisementServiceRpcClientFactory() {
        final AdvertisementServiceRpcClientFactory factory = new AdvertisementServiceRpcClientFactory(
                propertiesConfiguration.getBongoConfigDirectory(), AdvertisementServiceRpcClientFactory.Pool.READ_ONLY);
        factory.setClientApp(IndeedSystemProperty.APPLICATION.value());
        factory.extractApiKeySupplierFrom(context);
        factory.setDiscovery(IndeedDiscovery.ENVOY_MESH);
        return factory;
    }

    @Bean
    AdvertisementService advertisementService(
            final AdvertisementServiceRpcClientFactory advertisementServiceRpcClientFactory) {
        return advertisementServiceRpcClientFactory.get();
    }
}
