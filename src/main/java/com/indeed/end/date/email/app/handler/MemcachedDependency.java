package com.indeed.end.date.email.app.handler;

import com.indeed.status.core.PingableDependency;
import com.indeed.status.core.Urgency;
import jakarta.annotation.Nonnull;
import net.spy.memcached.MemcachedClient;

import java.util.concurrent.Future;

public class MemcachedDependency extends PingableDependency {

    private static final String HC_KEY = "DradisAdcentralEventsDaemon-HC-KEY";

    private static final int MEMCACHED_TIMEOUT = 10000;

    private final MemcachedClient memcachedClient;

    public MemcachedDependency(
            @Nonnull final MemcachedClient memcachedClient, @Nonnull final Urgency urgency, final int timeoutMs) {
        super("memcached", "memcached", timeoutMs, urgency);
        this.memcachedClient = memcachedClient;
    }

    @Override
    public void ping() throws Exception {
        final Future<Boolean> result = memcachedClient.add(HC_KEY, MEMCACHED_TIMEOUT, HC_KEY);
        result.get();
    }
}
