package com.indeed.end.date.email.app.util;

import com.indeed.advertiserservice.client.AdvertiserService;
import com.indeed.advertiserservice.client.AdvertiserServiceRpcClientException;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.Advertiser;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.AdvertiserProperty;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.BillingAddress;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetAdvertiserPropertyRequest;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetAdvertiserPropertyResponse;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetAdvertisersRequest;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetAdvertisersResponse;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetBillingAddressRequest;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetBillingAddressResponse;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.PropertyName;
import com.indeed.end.date.email.app.enums.ServiceName;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.util.core.time.WallClock;
import com.indeed.util.logging.Loggers;
import org.apache.log4j.Logger;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class AdvertiserUtil {

    private static final Logger LOG = Logger.getLogger(AdvertiserUtil.class);

    private final AdvertiserService advertiserService;
    private final MetricsEmitter metricsEmitter;
    private final WallClock wallClock;

    public AdvertiserUtil(
            final AdvertiserService advertiserService, final MetricsEmitter metricsEmitter, final WallClock wallClock) {
        this.advertiserService = advertiserService;
        this.metricsEmitter = metricsEmitter;
        this.wallClock = wallClock;
    }

    /**
     * Returns an optional containing billing country (like "US") for a given ACCOUNT id. If the billing country couldn't be
     * found, it returns an empty optional
     */
    public Optional<String> getBillingCountryForAdvertiserId(final int advertiserId) {
        if (advertiserId <= 0) {
            Loggers.warn(LOG, "advertiserId [%d] is invalid, so cannot fetch billing country", advertiserId);
            return Optional.empty();
        }

        final GetBillingAddressRequest request = GetBillingAddressRequest.newBuilder()
                .addAdvertiserId(advertiserId)
                .build();

        final GetBillingAddressResponse response;
        try {
            response = advertiserService.getBillingAddresses(request);
        } catch (final AdvertiserServiceRpcClientException e) {
            Loggers.error(LOG, "Unable to get billing country for advertiserId [%d]", e, advertiserId);
            return Optional.empty();
        }

        if (response.getBillingAddressCount() > 0) {
            final BillingAddress billingAddress = response.getBillingAddress(0);
            // billingAddress !=null is always true
            if (billingAddress.hasCountry()) {
                return Optional.of(billingAddress.getCountry());
            }
        }

        return Optional.empty();
    }

    public List<AdvertiserProperty> getAdvertiserPropertiesUnsafe(
            final int advertiserId, final List<PropertyName> propertyNames) throws AdvertiserServiceRpcClientException {
        if (advertiserId < 1) {
            Loggers.warn(
                    LOG,
                    "advertiserId [%d] is invalid, so cannot get AdvertiserProperty %s",
                    advertiserId,
                    propertyNames);
            return Collections.emptyList();
        }

        if (propertyNames == null || propertyNames.isEmpty()) {
            Loggers.error(LOG, "Property names list cannot be null or empty");
            return Collections.emptyList();
        }

        final GetAdvertiserPropertyRequest.Builder requestBuilder =
                GetAdvertiserPropertyRequest.newBuilder().setAdvertiserId(advertiserId);

        propertyNames.forEach(requestBuilder::addPropertyName);

        final GetAdvertiserPropertyResponse response =
                advertiserService.getAdvertiserProperties(requestBuilder.build());
        return response.getAdvertiserPropertyList();
    }

    public Optional<Advertiser> getAdvertiserFromAdvertiserIdUnsafe(final int advertiserId)
            throws AdvertiserServiceRpcClientException {
        if (advertiserId < 1) {
            Loggers.warn(LOG, "advertiserId [%d] is invalid, so cannot get Advertiser", advertiserId);
            return Optional.empty();
        }

        final GetAdvertisersRequest request =
                GetAdvertisersRequest.newBuilder().addAdvertiserId(advertiserId).build();

        final long startTimeMs = wallClock.currentTimeMillis();
        final GetAdvertisersResponse response = advertiserService.getAdvertisers(request);
        metricsEmitter.registerExternalServiceCall(ServiceName.ADVERTISER_SERVICE, startTimeMs);
        return (response == null || response.getAdvertiserCount() == 0)
                ? Optional.empty()
                : Optional.of(response.getAdvertiser(0));
    }
}
