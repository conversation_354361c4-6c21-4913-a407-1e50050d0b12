package com.indeed.end.date.email.app.util;

import com.indeed.logging.client.LogEntry;
import com.indeed.logging.client.LogEntryFactory;

import java.util.Map;

public class LogEntryUtil {

    private static final LogEntryFactory logEntryFactory = LogEntryFactory.getDefault();

    public void logTypeWithParamMap(final String logEntryType, final Map<String, String> loggingParams) {
        final LogEntry logEntry = logEntryFactory.createLogEntry(logEntryType);
        loggingParams.forEach(logEntry::setProperty);
        logEntry.commit();
    }
}
