package com.indeed.end.date.email.app.config;

import com.indeed.advertisementservice.client.AdvertisementService;
import com.indeed.advertiserservice.client.AdvertiserService;
import com.indeed.monetization.platform.library.core.adtrafficstats.GetDailyAdTrafficStats;
import com.indeed.monetization.platform.library.core.onegraph.OneGraphQueryClient;
import com.indeed.monetization.platform.library.core.utils.AdvertisementUtil;
import com.indeed.monetization.platform.library.core.utils.AdvertiserUtil;
import com.indeed.monetization.platform.library.core.utils.GetAdvertisementIDForJobUtil;
import com.indeed.monetization.platform.library.core.utils.GetAllLegacyJobIdsAndAdvertisementIdsUtil;
import com.indeed.monetization.platform.library.core.utils.GetJobContentForDailyAdTrafficStatsUtil;
import com.indeed.reporting.service.client.ReportingService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Nonnull;

@Configuration
@ComponentScan(basePackages = "com.indeed.monetization.platform.library.core")
public class GetDailyAdTrafficStatsConfig {
    @Bean(name = "advertiserUtilMonetization")
    public AdvertiserUtil advertiserUtil(@Nonnull final AdvertiserService advertiserService) {
        return new AdvertiserUtil(advertiserService, advertiserService);
    }

    @Bean
    public AdvertisementUtil advertisementUtil(@Nonnull final AdvertisementService advertisementService) {
        return new AdvertisementUtil(advertisementService, advertisementService);
    }

    @Bean
    public GetAdvertisementIDForJobUtil GetAdvertisementIDForJobUtil(
            @Nonnull final OneGraphQueryClient oneGraphQueryClient) {
        return new GetAdvertisementIDForJobUtil(oneGraphQueryClient);
    }

    @Bean
    public GetAllLegacyJobIdsAndAdvertisementIdsUtil GetAllLegacyJobIdsAndAdvertisementIdsUtil(
            @Nonnull final OneGraphQueryClient oneGraphQueryClient) {
        return new GetAllLegacyJobIdsAndAdvertisementIdsUtil(oneGraphQueryClient);
    }

    @Bean
    public GetJobContentForDailyAdTrafficStatsUtil GetJobContentForDailyAdTrafficStatsUtil(
            @Nonnull final OneGraphQueryClient oneGraphQueryClient) {
        return new GetJobContentForDailyAdTrafficStatsUtil(oneGraphQueryClient);
    }

    @Bean
    public GetDailyAdTrafficStats getDailyAdTrafficStats(
            @Nonnull final ReportingService reportingService,
            @Qualifier("advertiserUtilMonetization") final AdvertiserUtil advertiserUtil,
            @Nonnull final AdvertisementUtil advertisementUtil,
            @Nonnull final GetAdvertisementIDForJobUtil getAdvertisementIDForJobUtil,
            @Nonnull final GetAllLegacyJobIdsAndAdvertisementIdsUtil getAllLegacyJobIdsAndAdvertisementIdsUtil,
            @Nonnull final GetJobContentForDailyAdTrafficStatsUtil getJobContentForDailyAdTrafficStatsUtil) {
        return new GetDailyAdTrafficStats(
                reportingService,
                advertiserUtil,
                advertisementUtil,
                getAdvertisementIDForJobUtil,
                getAllLegacyJobIdsAndAdvertisementIdsUtil,
                getJobContentForDailyAdTrafficStatsUtil);
    }
}
