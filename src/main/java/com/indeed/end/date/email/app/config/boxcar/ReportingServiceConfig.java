package com.indeed.end.date.email.app.config.boxcar;

import com.indeed.common.base.IndeedSystemProperty;
import com.indeed.grpc.IndeedDiscovery;
import com.indeed.reporting.service.client.ReportingService;
import com.indeed.reporting.service.client.ReportingServiceRpcClientFactory;
import com.indeed.status.core.PingMethod;
import com.indeed.status.core.PingableDependency;
import com.indeed.status.core.SimplePingableDependency;
import com.indeed.status.core.Urgency;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@AllArgsConstructor
public class ReportingServiceConfig {

    private final ApplicationContext context;

    @Bean(destroyMethod = "shutdown")
    public ReportingService reportingService() {
        final ReportingServiceRpcClientFactory factory = new ReportingServiceRpcClientFactory();
        factory.setDiscovery(IndeedDiscovery.ENVOY_MESH);
        factory.extractApiKeySupplierFrom(context);
        factory.setClientApp(IndeedSystemProperty.APPLICATION.value());
        return factory.get();
    }

    @Bean
    public PingableDependency reportingServiceDependency(final ReportingService reportingService) {
        return SimplePingableDependency.newBuilder()
                .setId("reportingService")
                .setDescription("reportingService is used to get advertiser campaign metrics data")
                .setUrgency(Urgency.STRONG)
                .setDocumentationUrl("https://wiki.indeed.com/display/AdvSys/Reporting+Service")
                .setPingMethod((PingMethod) reportingService::probe)
                .build();
    }
}
