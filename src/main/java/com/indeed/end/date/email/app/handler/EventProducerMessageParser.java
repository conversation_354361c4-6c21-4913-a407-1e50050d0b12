package com.indeed.end.date.email.app.handler;

import com.google.protobuf.InvalidProtocolBufferException;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage;
import com.indeed.common.message.Message;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class EventProducerMessageParser {
    public EventProducerMessage parseMessage(@Nonnull final Message message) {
        try {
            return EventProducerMessage.parseFrom(message.getBody());
        } catch (final InvalidProtocolBufferException e) {
            log.error("Failed to parse provided bytes into an EventProducerMessage", e);
            return null;
        }
    }
}
