package com.indeed.end.date.email.app.exception;

import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage;

public class InvalidEventProducerMessageException extends RuntimeException {

    public InvalidEventProducerMessageException(final String msg) {
        super(msg);
    }

    public InvalidEventProducerMessageException(final String msg, final EventProducerMessage event) {
        super(msg + "\n" + event.toString());
    }
}
