package com.indeed.end.date.email.app.onegraph.util;

import com.apollographql.apollo3.ApolloCall;
import com.apollographql.apollo3.ApolloClient;
import com.apollographql.apollo3.api.ApolloResponse;
import com.apollographql.apollo3.api.Operation;
import com.apollographql.apollo3.api.Query;
import com.apollographql.apollo3.rx3.Rx3Apollo;
import com.indeed.end.date.email.app.model.HttpHeader;
import com.indeed.end.date.email.app.onegraph.exception.ApolloQueryResolutionException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
@Slf4j
public class ApolloResolver<T extends Operation.Data & Query.Data> {
    private final ApolloClient.Builder oneGraphApolloClientBuilder;

    public T resolveApolloQuery(final Query<T> query, final List<HttpHeader> headers) {
        final ApolloClient requestClient = makeRequestClient(headers);
        try {
            final ApolloCall<T> apolloCall = requestClient.query(query);
            final ApolloResponse<T> response = Rx3Apollo.single(apolloCall).blockingGet();
            if (response.hasErrors()) {
                assert response.errors != null;
                response.errors.forEach(error -> log.info(error.getMessage()));
                throw new ApolloQueryResolutionException("Response has Errors");
            }
            return response.data;
        } finally {
            requestClient.close();
        }
    }

    private ApolloClient makeRequestClient(final List<HttpHeader> headers) {
        ApolloClient.Builder clientBuilder = oneGraphApolloClientBuilder.copy();
        for (final HttpHeader header : headers) {
            clientBuilder = clientBuilder.addHttpHeader(header.getName(), header.getValue());
        }
        return clientBuilder.build();
    }
}
