package com.indeed.end.date.email.app.handler;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.protobuf.InvalidProtocolBufferException;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage.ActivityType;
import com.indeed.common.message.ConsumerListenerAdapter;
import com.indeed.common.message.ConsumerResponse;
import com.indeed.common.message.Message;
import com.indeed.common.message.MessageProducer;
import com.indeed.end.date.email.app.adceventsdaemon.handler.AdCentralEventHandler;
import com.indeed.end.date.email.app.exception.InvalidEventProducerMessageException;
import com.indeed.end.date.email.app.util.Constants;
import com.indeed.end.date.email.app.util.EndDateEmailStatsEmitter;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Closeable;
import java.io.IOException;
import java.time.Clock;
import java.util.List;
import java.util.Map;

/**
 * A callback that reads from the AdCentral events queue and routes events to the appropriate AdCentralEventHandler
 * <p>
 * The subscribed topics (i.e. activity types) are configured in AdCentralSubscribedTopics
 */
@Component
@Slf4j
public class AdCentralEventCallback implements ConsumerListenerAdapter, Closeable {
    @VisibleForTesting
    final Map<ActivityType, AdCentralEventHandler> activityHandlers = Maps.newEnumMap(ActivityType.class);

    private static final int ADCENTRAL_NUMBER_OF_RETRIES = 1;

    private static final int ADCENTRAL_NUMBER_OF_REQUEUES = 30;

    private final EventUIDCache eventUIDCache;
    private final MessageProducer requeueProducer;
    private final EndDateEmailStatsEmitter endDateEmailStatsEmitter;
    private final Clock clock;
    private final EventProducerMessageParser eventProducerMessageParser;

    public AdCentralEventCallback(
            @Nonnull final List<AdCentralEventHandler> handlerList,
            @Nonnull final EventUIDCache eventUIDCache,
            @Nonnull final MessageProducer requeueProducer,
            @Nonnull final EndDateEmailStatsEmitter endDateEmailStatsEmitter,
            @Nonnull final Clock clock,
            @Nonnull final EventProducerMessageParser eventProducerMessageParser) {
        Preconditions.checkNotNull(handlerList);
        Preconditions.checkNotNull(eventUIDCache);
        for (final AdCentralEventHandler handler : handlerList) {
            for (final ActivityType activityType : handler.getRegisteredActivityTypes()) {
                registerHandler(activityType, handler);
            }
        }
        this.requeueProducer = requeueProducer;
        this.eventUIDCache = eventUIDCache;
        this.endDateEmailStatsEmitter = endDateEmailStatsEmitter;
        this.clock = clock;
        this.eventProducerMessageParser = eventProducerMessageParser;
    }

    @VisibleForTesting
    void registerHandler(@Nonnull final ActivityType activityType, @Nonnull final AdCentralEventHandler handler) {
        /**
         * Check that no 2 handlers are registered for the same ActivityType.
         * Currently, the Daemon reads from a single RMQ. If multiple handlers are used we introduce the possibility of
         * one handler ack-ing a message and one that re-queues the message. The first handler will then incorrectly
         * process the message twice.
         */
        if (activityHandlers.get(activityType) != null) {
            throw new RuntimeException(String.format(
                    "Multiple handlers are attempting to register for the same ActivityType. [ActivityType=%s, Handler1 = %s, Handler2 = %s",
                    activityType.toString(),
                    activityHandlers.get(activityType).getClass().getSimpleName(),
                    handler.getClass().getSimpleName()));
        }
        activityHandlers.put(activityType, handler);
    }

    @Override
    public void onMessage(@Nonnull final Message message) {
        ConsumerResponse response = ConsumerResponse.NACK_WITH_REQUEUE;
        final EventProducerMessage event = eventProducerMessageParser.parseMessage(message);
        if (event == null) {
            message.ack();
            endDateEmailStatsEmitter.logInvalid();
            return;
        }
        try {
            final long handleStartTime = clock.millis();
            response = handleEvent(event);
            endDateEmailStatsEmitter.logTime(event.getActivityType(), clock.millis() - handleStartTime);
        } catch (final Exception e) {
            log.error("Unexpected exception when processing event, NACK_WITH_REQUEUE: ", e);
        }
        processResponse(message, response, event);
    }

    protected void processResponse(
            @Nonnull final Message message, final ConsumerResponse response, final EventProducerMessage event) {
        final ActivityType activityType = event.getActivityType();
        final String messageUID = event.getUid();

        if (response == ConsumerResponse.ACK) {
            endDateEmailStatsEmitter.logSuccess(activityType);
            if (message.getBooleanProperty(Constants.MESSAGE_IS_REQUEUED_PROPERTY_NAME, false)) {
                endDateEmailStatsEmitter.logSuccessAfterRequeue(activityType);
            } else if (message.getBooleanProperty(Constants.MESSAGE_IS_RETRIED_PROPERTY_NAME, false)) {
                endDateEmailStatsEmitter.logSuccessAfterRetry(activityType);
            }
            endDateEmailStatsEmitter.logNumberOfRetries(
                    activityType, message.getIntegerProperty(Constants.MESSAGE_NUMBER_OF_ATTEMPTS_PROPERTY_NAME, 0));
            message.ack();

        } else if (response == ConsumerResponse.NACK || response == ConsumerResponse.NACK_AS_FAILURE) {
            log.info(String.format("nack message with UID {}", messageUID));
            message.nack(false);
            endDateEmailStatsEmitter.logFailure(activityType);
        } else if (response == ConsumerResponse.NACK_WITH_REQUEUE
                || response == ConsumerResponse.NACK_WITH_REQUEUE_AS_FAILURE) {
            final int numberOfRetries =
                    message.getIntegerProperty(Constants.MESSAGE_NUMBER_OF_ATTEMPTS_PROPERTY_NAME, 0);
            log.debug("message with UID {} has {} Retries", messageUID, numberOfRetries);
            message.setIntegerProperty(Constants.MESSAGE_NUMBER_OF_ATTEMPTS_PROPERTY_NAME, numberOfRetries + 1);

            if (numberOfRetries < ADCENTRAL_NUMBER_OF_RETRIES) {
                log.info("retry message with UID {}", messageUID);
                message.setBooleanProperty(Constants.MESSAGE_IS_RETRIED_PROPERTY_NAME, true);
                onMessage(message);
            } else if (numberOfRetries < ADCENTRAL_NUMBER_OF_RETRIES + ADCENTRAL_NUMBER_OF_REQUEUES) {
                final boolean isMessageReQueuedBefore =
                        message.getBooleanProperty(Constants.MESSAGE_IS_REQUEUED_PROPERTY_NAME, false);
                try {
                    log.info("sending message with UID {} to delay queue", messageUID);
                    message.setBooleanProperty(Constants.MESSAGE_IS_REQUEUED_PROPERTY_NAME, true);
                    requeueProducer.send(message);
                } catch (Exception e) {
                    log.error("Could not requeue message with backoff. NACKing", e);
                    message.setBooleanProperty(Constants.MESSAGE_IS_REQUEUED_PROPERTY_NAME, isMessageReQueuedBefore);
                    message.nack(false);
                    endDateEmailStatsEmitter.logFailure(activityType);
                }
            } else {
                log.info("nack message with UID {}", messageUID);
                message.nack(false);
                endDateEmailStatsEmitter.logFailure(activityType);
            }

        } else {
            log.error("unhandled ConsumerResponse");
            log.info("nack message with UID {}", messageUID);
            message.nack(false);
            endDateEmailStatsEmitter.logFailure(activityType);
        }
    }

    private ConsumerResponse handleEvent(@Nonnull final EventProducerMessage event) {
        if (!event.hasActivityType()) {
            log.error("EventProducerMessage does not have activityType, will NACK");
            return ConsumerResponse.NACK;
        }
        final ActivityType activityType = event.getActivityType();

        @Nullable final AdCentralEventHandler handler = activityHandlers.get(activityType);

        if (handler == null) {
            /*So that the DLQ doesn't fill up when removing the handler for an activity. When removing a handler, the
            activity type/routing key will still be bound in the queue until we get RMQ SREs to clean up the binding
            for us, meaning the handler may be gone but the queue is still receiving messages for the type it handled.
            If we NACK'd them as before, we fill up the DLQ.
            */
            log.error("No handler for activity type [" + activityType.name() + "], will ACK");
            return ConsumerResponse.ACK;
        }

        log.debug("Found registered handler [" + handler.getClass().getSimpleName() + "] for activity type ["
                + activityType.name() + "]");
        if (event.hasUid()) {
            final EventUIDCache.GetCacheStatus getProcessedCacheStatus =
                    eventUIDCache.isUIDInProcessedCache(event.getUid());
            if (getProcessedCacheStatus == EventUIDCache.GetCacheStatus.IN_CACHE) {
                log.debug("ACKing UID = " + event.getUid() + " because UID is in processed cache.");
                return ConsumerResponse.ACK;
            } else if (getProcessedCacheStatus == EventUIDCache.GetCacheStatus.ERROR) {
                log.trace("Requeueing UID = " + event.getUid()
                        + " because of failure to determine if UID was in processed cache.");
                return ConsumerResponse.NACK_WITH_REQUEUE;
            }
            final EventUIDCache.AddCacheStatus cacheStatus = eventUIDCache.safeAddUIDToProcessingCache(event.getUid());
            if (EventUIDCache.AddCacheStatus.ADDED_TO_CACHE != cacheStatus) {
                log.trace(
                        "Requeueing UID = {} because UID could not be added to the processing cache.", event.getUid());
                return ConsumerResponse.NACK_WITH_REQUEUE;
            }
        }

        try {
            final ConsumerResponse handlerResponse = handler.handleEvent(event);
            setUidToProcessed(event, handlerResponse);
            return handlerResponse;
        } catch (InvalidEventProducerMessageException e) {
            log.error("InvalidEventProducerMessageException when processing event, NACK: ", e);
            setUidToProcessed(event, ConsumerResponse.NACK);
            return ConsumerResponse.NACK;
        } catch (Exception e) {
            log.error("Exception when processing event, NACK_WITH_REQUEUE: ", e);
            setUidToProcessed(event, ConsumerResponse.NACK_WITH_REQUEUE);
            return ConsumerResponse.NACK_WITH_REQUEUE;
        }
    }

    @Nonnull
    @Override
    public ConsumerResponse handle(@Nonnull final byte[] encodedBytes) {
        final EventProducerMessage event;
        try {
            event = EventProducerMessage.parseFrom(encodedBytes);
        } catch (final InvalidProtocolBufferException e) {
            log.error("Failed to parse provided bytes into an EventProducerMessage", e);
            endDateEmailStatsEmitter.logInvalid();
            return ConsumerResponse.ACK;
        }
        return handleEvent(event);
    }

    private void setUidToProcessed(
            @Nonnull final EventProducerMessage event, @Nonnull final ConsumerResponse handlerResponse) {
        if (event.hasUid()) {
            if (handlerResponse != ConsumerResponse.NACK_WITH_REQUEUE) {
                eventUIDCache.safeAddUIDToProcessedCache(event.getUid());
            }
            eventUIDCache.safeRemoveFromProcessingCache(event.getUid());
        }
    }

    @Override
    public void close() throws IOException {
        // A handler can have multiple mappings, so convert to a set so we only close each handler once
        for (final AdCentralEventHandler handler : Sets.newHashSet(activityHandlers.values())) {
            try {
                handler.close();
            } catch (Exception e) {
                log.error("Error closing handler of type {}", handler.getClass().getName(), e);
            }
        }
    }
}
