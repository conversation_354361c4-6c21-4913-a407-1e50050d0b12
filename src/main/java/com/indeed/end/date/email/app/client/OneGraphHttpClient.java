package com.indeed.end.date.email.app.client;

import okhttp3.OkHttpClient;

public class OneGraphHttpClient {
    private final OkHttpClient httpClient;

    public OneGraphHttpClient() {
        httpClient = new OkHttpClient.Builder().retryOnConnectionFailure(true).build();
    }

    public OkHttpClient getHttpClient() {
        return httpClient;
    }

    public void dispose() {
        httpClient.dispatcher().cancelAll();
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
}
