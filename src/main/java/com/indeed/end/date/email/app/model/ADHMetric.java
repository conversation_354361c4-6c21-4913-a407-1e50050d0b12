package com.indeed.end.date.email.app.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Getter;

import java.util.Locale;
import java.util.Map;

/**
 * Model for the ADH Metric used for requesting data. Each metric is keyed on a string name identifier.
 * See {@link com.indeed.dradis.tools.adh.ADHMetricsFacade} METRIC_NAMES for the keys of the metrics that
 * the job is live email uses.
 *
 * Example of data within each ADH Metric.
 * {
 *   "metric_id": 9626,
 *   "local_format_value": {
 *     "en": "53.4M",
 *     "ar": "‎مليون 53.4",
 *     "cs": "53,4 milionů",
 *     "da": "53,4 mio.",
 *     "de": "53,4 Mio.",
 *     "de_CH": "53.4 Mio.",
 *     "el": "53,4 εκατ.",
 *     "es": "53,4 millones",
 *     "es_MX": "53.4 millones",
 *     "fi": "53,4 miljoonaa",
 *     "fr": "53,4 millions",
 *     "hu": "53,4 millió",
 *     "in": "53,4M",
 *     "it": "53,4 Mln",
 *     "it_CH": "53.4 Mln",
 *     "iw": "53.4M",
 *     "ja": "5340.0万",
 *     "ko": "5340.0만",
 *     "nl": "53,4 mln.",
 *     "no": "53,4 mill.",
 *     "pl": "53,4 miliony",
 *     "pt": "53,4 milhões",
 *     "ro": "53,4 de miloane",
 *     "ru": "53,4 млн",
 *     "sv": "53,4 miljoner",
 *     "th": "53.4 ล้าน",
 *     "tr": "53,4 milyon",
 *     "uk": "53,4 млн",
 *     "vi": "53,4 triệu",
 *     "zh": "5340.0万",
 *     "zh_HK": "5340.0萬",
 *     "zh_TW": "5340.0萬"
 *   },
 *   "local_date_of_data": {
 *     "en": "March 2021",
 *     "ar": "2021 مارس",
 *     "cs": "března 2021",
 *     "da": "marts 2021",
 *     "de": "März 2021",
 *     "de_CH": "März 2021",
 *     "el": "Μαρτίου 2021",
 *     "es": "marzo de 2021",
 *     "es_MX": "marzo de 2021",
 *     "fi": "maaliskuuta 2021",
 *     "fr": "mars 2021",
 *     "hu": "2021. március",
 *     "in": "Maret 2021",
 *     "it": "marzo 2021",
 *     "it_CH": "marzo 2021",
 *     "iw": "מרץ 2021",
 *     "ja": "2021年3月",
 *     "ko": "2021년3월",
 *     "nl": "maart 2021",
 *     "no": "mars 2021",
 *     "pl": "marca 2021",
 *     "pt": "março 2021",
 *     "ro": "martie 2021",
 *     "ru": "марта 2021 r.",
 *     "sv": "mars 2021",
 *     "th": "มีนาคม 2021",
 *     "tr": "Mart 2021",
 *     "uk": "березня 2021 p.",
 *     "vi": "tháng 3 năm 2021",
 *     "zh": "2021年三月",
 *     "zh_HK": "2021年3月",
 *     "zh_TW": "2021年3月"
 *   },
 *   "name": "Total visits per month",
 *   "value": "53400000",
 *   "citation_details": "",
 *   "require_citation": true,
 *   "legal_ref_id": "Key Data 01",
 *   "legal_link": "https://bugs.indeed.com/browse/DATACON-426",
 *   "update_frequency": "Monthly",
 *   "comments": "Citation: SimilarWeb, Total Visits, month year  ---> For citation translations and exceptions,
 *   visit https://go.indeed.com/DSWiki",
 *   "date_of_data": "March 2021",
 *   "api_name": "total_visits_per_month_uk",
 *   "status": "VISIBLE",
 *   "formatted_value": "53.4M",
 *   "methodology_details": null,
 *   "link_to_query": null,
 *   "date_of_data_formatted": "2021-01-03-01-99",
 *   "display_format": {
 *     "display_format_id": 8,
 *     "name": "0.0M"
 *   },
 *   "country": {
 *     "country_id": 512,
 *     "name": "UK",
 *     "country_name": "United Kingdom"
 *   },
 *   "data_category": {
 *     "data_category_id": 372,
 *     "name": "Indeed: Market Penetration"
 *   },
 *   "data_source": {
 *     "data_source_id": 407,
 *     "name": "SimilarWeb"
 *   }
 * }
 */
@Getter
@Builder
@JsonDeserialize(builder = ADHMetric.ADHMetricBuilder.class)
public final class ADHMetric {

    private final String dateOfData;

    private final String value;

    private final String formattedValue;

    private final String country;

    /**
     * This info is only used in the artifact map of metrics to find user's local formatted value. @JsonIgnore stops this field
     * from being serialized.
     */
    @JsonIgnore
    private final Map<String, String> localFormatValues;

    /**
     * Return the localized formatted value for the ADH Metric. This method attempts to look for the locale first
     * then attempts the language, then falls back on the non-localized (en-US default "formatted_value" field in artifact)
     * value.
     *
     * Example of the localFormatValue contents
     * {
     *       "en": "72%",
     *       "de": "72 %",
     *       "de_CH": "72%",
     * }
     *
     * @param locale The locale being requested.
     *
     * @return String Localized formatted value for the locale/language.
     */
    public String getLocalFormatValue(final Locale locale) {
        final String localeString = locale.toString();
        final String langString = locale.getLanguage();
        if (localFormatValues.containsKey(localeString)) {
            return localFormatValues.get(localeString);
        } else {
            return localFormatValues.getOrDefault(langString, formattedValue);
        }
    }

    @JsonPOJOBuilder(withPrefix = "")
    public static class ADHMetricBuilder {

        @JsonProperty("date_of_data")
        private String dateOfData;

        private String value;

        @JsonProperty("formatted_value")
        private String formattedValue;

        @JsonProperty("local_format_value")
        private Map<String, String> localFormatValues;

        private String country;

        @JsonProperty("country")
        private void getCountryName(final Map<String, String> countryObj) {
            country = countryObj.get("name");
        }
    }
}
