package com.indeed.end.date.email.app.util;

import com.indeed.common.util.StringUtils;
import com.indeed.end.date.email.app.enums.ServiceName;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.end.date.email.app.model.RavenEmail;
import com.indeed.end.date.email.app.model.RavenEmail.RavenEmailBuilder;
import com.indeed.raven.delivery.client.DeliverCampaignSession;
import com.indeed.raven.delivery.client.RavenDeliveryClient;
import com.indeed.raven.delivery.client.RavenDeliveryRpcClientException;
import com.indeed.raven.delivery.rpc.DeliverCampaignResponse;
import com.indeed.util.core.time.WallClock;
import com.indeed.util.logging.Loggers;
import org.apache.log4j.Logger;

import java.util.Locale;
import java.util.Map;
import java.util.Optional;

public class EmailUtil {

    private static final Logger LOG = Logger.getLogger(EmailUtil.class);

    private final RavenDeliveryClient ravenDeliveryClient;
    private final MetricsEmitter metricsEmitter;
    private final WallClock wallClock;

    public EmailUtil(
            final RavenDeliveryClient ravenDeliveryClient,
            final MetricsEmitter metricsEmitter,
            final WallClock wallClock) {
        this.ravenDeliveryClient = ravenDeliveryClient;
        this.metricsEmitter = metricsEmitter;
        this.wallClock = wallClock;
    }

    public Optional<RavenEmail> sendEmailUnsafe(
            final int advertiserId, final String campaignName, final Map<String, Object> dataMap, final Locale locale)
            throws RavenDeliveryRpcClientException {
        if (advertiserId < 1) {
            Loggers.error(LOG, "AdvertiserId [%d] is invalid", advertiserId);
            return Optional.empty();
        }

        if (StringUtils.isEmpty(campaignName)) {
            Loggers.error(LOG, "Raven campaign name cannot be null or empty");
            return Optional.empty();
        }

        final long startTimeMs = wallClock.currentTimeMillis();
        final DeliverCampaignSession session = ravenDeliveryClient.startCampaign(campaignName);
        session.newBuilder()
                .withRecipientId(advertiserId)
                .withDataMap(dataMap)
                .withLanguage(locale.getLanguage())
                .withCountry(locale.getCountry())
                .build();
        final DeliverCampaignResponse ravenResponse = session.send();
        metricsEmitter.registerExternalServiceCall(ServiceName.RAVEN, startTimeMs);

        final RavenEmailBuilder ravenEmailBuilder = RavenEmail.builder()
                .advertiserId(advertiserId)
                .campaignName(campaignName)
                .dataMap(dataMap);
        ravenEmailBuilder.ravenSessionUid(session.getCampaignUid().toString());
        if (ravenResponse.getRecipientResultCount() > 0) {
            ravenEmailBuilder.status(ravenResponse.getRecipientResult(0).getStatus());
        }
        return Optional.of(ravenEmailBuilder.build());
    }
}
