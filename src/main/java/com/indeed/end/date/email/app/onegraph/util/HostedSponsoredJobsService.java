package com.indeed.end.date.email.app.onegraph.util;

import com.indeed.end.date.email.app.enums.ServiceName;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.end.date.email.app.model.EndDateEmailJobData;
import com.indeed.end.date.email.app.model.HttpHeader;
import com.indeed.one.graph.client.HostedJobPostsQuery;
import com.indeed.one.graph.client.type.CampaignExtRefType;
import com.indeed.one.graph.client.type.EmployerJobCampaignsConnectionFilterInput;
import com.indeed.one.graph.client.type.EmployerJobCampaignsConnectionInput;
import com.indeed.one.graph.client.type.JobToCampaignLookupStrategy;
import com.indeed.util.core.time.WallClock;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class HostedSponsoredJobsService {

    private static final String ONEGRAPH_ADVID_HEADER_NAME = "indeed-advertiser-id";

    private static final DateFormat EXPECTED_END_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm");

    private final IDUtilService idUtilService;

    private ApolloResolver<HostedJobPostsQuery.Data> apolloResolver;
    private final MetricsEmitter metricsEmitter;
    private final WallClock wallClock;

    public EndDateEmailJobData getJob(final int advertiserId, final int atsJobId) throws Exception {
        try {
            final String legacyJobId = idUtilService.atsJobIdToLegacyJobId(atsJobId, advertiserId);
            final List<HttpHeader> headers =
                    Collections.singletonList(new HttpHeader(ONEGRAPH_ADVID_HEADER_NAME, String.valueOf(advertiserId)));

            final long startTimeMs = wallClock.currentTimeMillis();
            // Create campaigns connection input
            final EmployerJobCampaignsConnectionInput campaignsConnectionInput = EmployerJobCampaignsConnectionInput.builder()
                    .lookupStrategies(Collections.singletonList(JobToCampaignLookupStrategy.JOB_GROUP))
                    .filter(EmployerJobCampaignsConnectionFilterInput.builder()
                            .isHosted(true)
                            .extRefTypes(Collections.singletonList(CampaignExtRefType.SMB_SINGLE_JOB))
                            .build())
                    .build();

            final HostedJobPostsQuery.Data responseData = apolloResolver.resolveApolloQuery(
                    new HostedJobPostsQuery(Collections.singletonList(legacyJobId), campaignsConnectionInput), headers);
            metricsEmitter.registerExternalServiceCall(ServiceName.ONE_GRAPH, startTimeMs);

            if (responseData.hostedJobPostsByLegacyIds.results.size() > 0) {
                final HostedJobPostsQuery.HostedJobPost hostedJobPost =
                        responseData.hostedJobPostsByLegacyIds.results.get(0).hostedJobPost;

                Date endDate = null;
                boolean isFreeJob = true;

                // Check for active or paused campaigns
                if (hostedJobPost.employerJob != null && 
                    hostedJobPost.employerJob.campaignsConnection != null && 
                    hostedJobPost.employerJob.campaignsConnection.edges != null) {
                    
                    Optional<HostedJobPostsQuery.Node> activeCampaign = hostedJobPost.employerJob.campaignsConnection.edges.stream()
                            .map(edge -> edge.node)
                            .filter(node -> "ACTIVE".equals(node.status) || "PAUSED".equals(node.status))
                            .findFirst();
                    
                    if (activeCampaign.isPresent()) {
                        isFreeJob = false;
                        
                        // Get end date from campaign schedule
                        if (activeCampaign.get().schedule != null && 
                            activeCampaign.get().schedule.endDateTime != null) {
                            try {
                                endDate = EXPECTED_END_DATE_FORMAT.parse(
                                        activeCampaign.get().schedule.endDateTime);
                            } catch (ParseException pe) {
                                log.error(
                                        "Unable to parse enddate {} for advertiser {}, atsJobId {} ",
                                        activeCampaign.get().schedule.endDateTime,
                                        advertiserId,
                                        atsJobId,
                                        pe);
                                return null;
                            }
                        }
                    }
                }

                final List<String> advertisingLocations = hostedJobPost.advertisingLocations.stream()
                        .filter(adLoc -> adLoc.active)
                        .map(adLoc -> adLoc.location)
                        .collect(Collectors.toList());

                final EndDateEmailJobData jobData = EndDateEmailJobData.builder()
                        .advertiserId(advertiserId)
                        .atsJobId(atsJobId)
                        .freeJob(isFreeJob)
                        .advertisingLocations(advertisingLocations)
                        .company(hostedJobPost.company)
                        .title(hostedJobPost.title)
                        .endDate(endDate)
                        .build();

                return jobData;
            }
        } catch (final Exception e) {
            log.error(
                    "Exception calling HostedSponsoredJobsService for advertiserId:{}, atsJobId:{} ",
                    advertiserId,
                    atsJobId,
                    e);
            throw e;
        }

        return null;
    }
}
