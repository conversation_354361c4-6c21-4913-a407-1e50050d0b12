package com.indeed.end.date.email.app.util;

import com.google.common.collect.ImmutableList;
import com.indeed.adcentral.proto.events.EventProducerProto;
import com.indeed.end.date.email.app.enums.ErrorCause;
import com.indeed.end.date.email.app.enums.ServiceName;
import com.indeed.logging.client.Tag;
import com.indeed.logging.client.stats.StatsEmitter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_EMAIL_SENT;
import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_SERVICE_FAILURE;
import static com.indeed.end.date.email.app.enums.ServiceName.END_DATE_EMAIL;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SEND_ERROR_CAUSE;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SEND_SUCCESS;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SERVICE_NAME;

@Service
@AllArgsConstructor
public class EndDateEmailStatsEmitter {

    private final String PREFIX = "adCentral_events.";
    private final String GENERAL_IDENTIFIER = "ALL_EVENTS";

    private final StatsEmitter statsEmitter;

    public void logTime(final EventProducerProto.EventProducerMessage.ActivityType activityType, final long duration) {
        statsEmitter.histogram(String.format("%s%s.time", PREFIX, activityType.name()), duration);
        statsEmitter.histogram(String.format("%s%s.time", PREFIX, GENERAL_IDENTIFIER), duration);
    }

    public void logNumberOfRetries(
            final EventProducerProto.EventProducerMessage.ActivityType activityType, final int numberOfRetries) {
        statsEmitter.histogram(
                String.format("%s%s.attempts_before_success", PREFIX, activityType.name()), numberOfRetries);
        statsEmitter.histogram(
                String.format("%s%s.attempts_before_success", PREFIX, GENERAL_IDENTIFIER), numberOfRetries);
    }

    public void logFailure(final EventProducerProto.EventProducerMessage.ActivityType activityType) {
        logCount(activityType, "failure");
    }

    public void logSuccess(final EventProducerProto.EventProducerMessage.ActivityType activityType) {
        logCount(activityType, "success");
    }

    public void logSuccessAfterRetry(final EventProducerProto.EventProducerMessage.ActivityType activityType) {
        logCount(activityType, "success_after_retry");
    }

    public void logSuccessAfterRequeue(final EventProducerProto.EventProducerMessage.ActivityType activityType) {
        logCount(activityType, "success_after_requeue");
    }

    public void logInvalid() {
        statsEmitter.count(PREFIX + ".invalid");
    }

    private void logCount(
            final EventProducerProto.EventProducerMessage.ActivityType activityType, final String logType) {
        statsEmitter.count(String.format("%s%s.%s", PREFIX, activityType.name(), logType));
        statsEmitter.count(String.format("%s%s.%s", PREFIX, GENERAL_IDENTIFIER, logType));
    }

    public void emitEndDatEmailSent(final long startTime) {
        final List<Tag> tags = ImmutableList.of(
                new Tag(TAG_SEND_SUCCESS, Boolean.toString(Boolean.TRUE)),
                new Tag(TAG_SERVICE_NAME, END_DATE_EMAIL.name()));

        statsEmitter.count(END_DATE_EMAIL_SENT, 1, tags);
    }

    public void emitEndDateEmailFailed(final ErrorCause errorCause) {
        final List<Tag> tags = ImmutableList.of(
                new Tag(TAG_SEND_SUCCESS, Boolean.toString(Boolean.FALSE)),
                new Tag(TAG_SEND_ERROR_CAUSE, errorCause.name()),
                new Tag(TAG_SERVICE_NAME, END_DATE_EMAIL.name()));

        statsEmitter.count(END_DATE_EMAIL_SENT, 1, tags);
    }

    public void emitCreditConfirmationEmailRequeued(final ServiceName service) {
        final List<Tag> tags = ImmutableList.of(new Tag(TAG_SERVICE_NAME, service.name()));

        statsEmitter.count(END_DATE_SERVICE_FAILURE, 1, tags);
    }
}
