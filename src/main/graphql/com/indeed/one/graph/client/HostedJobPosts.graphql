query HostedJobPosts($ids: [ID!]!, $campaignsConnectionInput: EmployerJobCampaignsConnectionInput) {
    hostedJobPostsByLegacyIds(ids: $ids) {
        results {
            hostedJobPost {
                company
                title
                advertisingLocations {
                  location
                  active
                }
                employerJob {
                    campaignsConnection(input: $campaignsConnectionInput) {
                        edges {
                            node {
                                id
                                status
                                budget {
                                    limit {
                                        amountLocal
                                        currency
                                    }
                                    recurringUnit
                                }
                                schedule {
                                    endDateTime
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
