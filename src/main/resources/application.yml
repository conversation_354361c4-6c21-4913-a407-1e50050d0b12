# bongo config. Not needed if you aren't connecting to any boxcar services.
bongo.config.dir: ${indeed.base}/conf
one-graph.base-url: http://${MESH_HOST_AND_PORT:localhost:${PORT4:4444}}/one-graph

# Do not mount Poodlepants to / since that is used by our IndexController
poodlepants.redirect-root: false

adcentral:
  rabbit:
    config:
      file: ${indeed.base}/conf/adcentral-aws-rabbit-consumer-config.properties
  number:
    of:
      retries: 1
      requeues: 2

---

spring:
  config:
    activate:
      on-profile: env-local
http.monitor.port: 8080
---

spring:
  config:
    activate:
      on-profile: env-qa
example.default.variable: qa-value

---

spring:
  config:
    activate:
      on-profile: env-prod
example.default.variable: prod-value
