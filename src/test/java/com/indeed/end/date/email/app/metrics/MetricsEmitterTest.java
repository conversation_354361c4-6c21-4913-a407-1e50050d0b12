package com.indeed.end.date.email.app.metrics;

import com.indeed.end.date.email.app.enums.ErrorCause;
import com.indeed.end.date.email.app.enums.ServiceName;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Clock;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_EMAIL_SENT;
import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_SERVICE_FAILURE;
import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_SERVICE_SUCCESS;
import static com.indeed.end.date.email.app.enums.ServiceName.END_DATE_EMAIL;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SEND_ERROR_CAUSE;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SEND_SUCCESS;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SERVICE_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MetricsEmitterTest {

    @Mock
    private MeterRegistry meterRegistry;

    @Mock
    private Clock clock;

    @Mock
    private Counter counter;

    @Mock
    private Timer timer;

    private MetricsEmitter emitter;

    @BeforeEach
    public void setUp() {
        emitter = new MetricsEmitter(meterRegistry, clock);
    }

    @Test
    public void GIVEN_counterSucceeds_WHEN_emitEndDateEmailSent_THEN_incrementCounter() {
        // GIVEN
        ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Iterable<Tag>> tagsCaptor = ArgumentCaptor.forClass(Iterable.class);

        when(meterRegistry.counter(eq(END_DATE_EMAIL_SENT), any(Iterable.class)))
                .thenReturn(counter);

        // WHEN
        emitter.emitEndDateEmailSent();

        // THEN
        verify(meterRegistry).counter(nameCaptor.capture(), tagsCaptor.capture());
        verify(counter, times(1)).increment();
        assertEquals(END_DATE_EMAIL_SENT, nameCaptor.getValue());

        final List<Tag> tags = toList(tagsCaptor.getValue());
        assertTrue(tags.contains(Tag.of(TAG_SERVICE_NAME, END_DATE_EMAIL.name())));
        assertTrue(tags.contains(Tag.of(TAG_SEND_SUCCESS, Boolean.toString(true))));
    }

    @Test
    public void GIVEN_counterSucceeds_WHEN_emitEndDateEmailFailed_THEN_incrementCounter() {
        // GIVEN
        ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Iterable<Tag>> tagsCaptor = ArgumentCaptor.forClass(Iterable.class);

        when(meterRegistry.counter(eq(END_DATE_EMAIL_SENT), any(Iterable.class)))
                .thenReturn(counter);

        // WHEN
        emitter.emitEndDateEmailFailed(ErrorCause.RAVEN_ERROR);

        // THEN
        verify(meterRegistry).counter(nameCaptor.capture(), tagsCaptor.capture());
        verify(counter, times(1)).increment();

        assertEquals(END_DATE_EMAIL_SENT, nameCaptor.getValue());
        final List<Tag> tags = toList(tagsCaptor.getValue());
        assertTrue(tags.contains(Tag.of(TAG_SERVICE_NAME, END_DATE_EMAIL.name())));
        assertTrue(tags.contains(Tag.of(TAG_SEND_SUCCESS, Boolean.toString(false))));
        assertTrue(tags.contains(Tag.of(TAG_SEND_ERROR_CAUSE, ErrorCause.RAVEN_ERROR.name())));
    }

    @Test
    public void GIVEN_counterSucceeds_WHEN_emitEndDateEmailRequeued_THEN_incrementFailureCounter() {
        // GIVEN
        ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Iterable<Tag>> tagsCaptor = ArgumentCaptor.forClass(Iterable.class);

        when(meterRegistry.counter(eq(END_DATE_SERVICE_FAILURE), any(Iterable.class)))
                .thenReturn(counter);

        // WHEN
        emitter.emitEndDateEmailRequeued(ServiceName.ONE_GRAPH);

        // THEN
        verify(meterRegistry).counter(nameCaptor.capture(), tagsCaptor.capture());
        verify(counter, times(1)).increment();

        assertEquals(END_DATE_SERVICE_FAILURE, nameCaptor.getValue());
        final List<Tag> tags = toList(tagsCaptor.getValue());
        assertTrue(tags.contains(Tag.of(TAG_SERVICE_NAME, ServiceName.ONE_GRAPH.name())));
    }

    @Test
    public void GIVEN_timerSucceeds_WHEN_registerExternalServiceCall_THEN_recordTimer() {
        // GIVEN
        ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Iterable<Tag>> tagsCaptor = ArgumentCaptor.forClass(Iterable.class);

        when(meterRegistry.timer(any(String.class), any(Iterable.class))).thenReturn(timer);
        when(clock.millis()).thenReturn(2_000L);

        // WHEN
        emitter.registerExternalServiceCall(ServiceName.CALLY, 1_500L);

        // THEN
        verify(meterRegistry).timer(nameCaptor.capture(), tagsCaptor.capture());
        verify(timer, times(1)).record(500L, TimeUnit.MILLISECONDS);

        assertEquals("end_date_email.call.process.time", nameCaptor.getValue());
        final List<Tag> tags = toList(tagsCaptor.getValue());
        assertTrue(tags.contains(Tag.of(TAG_SERVICE_NAME, ServiceName.CALLY.name())));
        assertTrue(tags.contains(Tag.of(END_DATE_SERVICE_SUCCESS, Boolean.toString(true))));
    }

    private static List<Tag> toList(final Iterable<Tag> iterable) {
        final List<Tag> list = new ArrayList<>();
        for (final Tag t : iterable) {
            list.add(t);
        }
        return list;
    }
}
