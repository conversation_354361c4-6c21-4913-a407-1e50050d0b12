package com.indeed.end.date.email.app.util;

import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage.ActivityType;
import com.indeed.end.date.email.app.enums.ErrorCause;
import com.indeed.end.date.email.app.enums.ServiceName;
import com.indeed.logging.client.Tag;
import com.indeed.logging.client.stats.StatsEmitter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_EMAIL_SENT;
import static com.indeed.end.date.email.app.enums.MetricNames.END_DATE_SERVICE_FAILURE;
import static com.indeed.end.date.email.app.enums.ServiceName.END_DATE_EMAIL;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SEND_ERROR_CAUSE;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SEND_SUCCESS;
import static com.indeed.end.date.email.app.enums.TagNames.TAG_SERVICE_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class EndDateEmailStatsEmitterTest {

    @Mock
    private StatsEmitter statsEmitter;

    private EndDateEmailStatsEmitter emitter;

    @BeforeEach
    public void setUp() {
        emitter = new EndDateEmailStatsEmitter(statsEmitter);
    }

    @Test
    public void GIVEN_activityType_WHEN_logTime_THEN_emitHistograms() {
        // GIVEN
        final ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);

        // WHEN
        emitter.logTime(ActivityType.PAUSE_JOB, 123L);

        // THEN
        verify(statsEmitter, times(2)).histogram(nameCaptor.capture(), eq(123L));

        final List<String> names = nameCaptor.getAllValues();
        assertTrue(names.contains("adCentral_events.PAUSE_JOB.time"));
        assertTrue(names.contains("adCentral_events.ALL_EVENTS.time"));
    }

    @Test
    public void GIVEN_attempts_WHEN_logNumberOfRetries_THEN_emitHistograms() {
        // GIVEN
        final ArgumentCaptor<String> namesCaptor = ArgumentCaptor.forClass(String.class);

        // WHEN
        emitter.logNumberOfRetries(ActivityType.PAUSE_JOB, 3);

        // THEN
        verify(statsEmitter, times(2)).histogram(namesCaptor.capture(), eq(3));

        final List<String> names = namesCaptor.getAllValues();
        assertTrue(names.contains("adCentral_events.PAUSE_JOB.attempts_before_success"));
        assertTrue(names.contains("adCentral_events.ALL_EVENTS.attempts_before_success"));
    }

    @Test
    public void GIVEN_activityType_WHEN_logFailure_THEN_count() {
        // GIVEN ActivityType = PAUSE_JOB

        // WHEN
        emitter.logFailure(ActivityType.PAUSE_JOB);

        // THEN
        verify(statsEmitter).count("adCentral_events.PAUSE_JOB.failure");
        verify(statsEmitter).count("adCentral_events.ALL_EVENTS.failure");
    }

    @Test
    public void GIVEN_activityType_WHEN_logSuccess_THEN_count() {
        // GIVEN ActivityType = PAUSE_JOB

        // WHEN
        emitter.logSuccess(ActivityType.PAUSE_JOB);

        // THEN
        verify(statsEmitter).count("adCentral_events.PAUSE_JOB.success");
        verify(statsEmitter).count("adCentral_events.ALL_EVENTS.success");
    }

    @Test
    public void GIVEN_activityType_WHEN_logSuccessAfterRetry_THEN_count() {
        // GIVEN ActivityType = PAUSE_JOB

        // WHEN
        emitter.logSuccessAfterRetry(ActivityType.PAUSE_JOB);

        // THEN
        verify(statsEmitter).count("adCentral_events.PAUSE_JOB.success_after_retry");
        verify(statsEmitter).count("adCentral_events.ALL_EVENTS.success_after_retry");
    }

    @Test
    public void GIVEN_activityType_WHEN_logSuccessAfterRequeue_THEN_count() {
        // GIVEN ActivityType = PAUSE_JOB

        // WHEN
        emitter.logSuccessAfterRequeue(ActivityType.PAUSE_JOB);

        // THEN
        verify(statsEmitter).count("adCentral_events.PAUSE_JOB.success_after_requeue");
        verify(statsEmitter).count("adCentral_events.ALL_EVENTS.success_after_requeue");
    }

    @Test
    public void GIVEN_noContext_WHEN_logInvalid_THEN_countInvalid() {
        // WHEN
        emitter.logInvalid();

        // THEN
        verify(statsEmitter).count("adCentral_events..invalid");
    }

    @Test
    public void GIVEN_statsEmitter_WHEN_emitEndDatEmailSent_THEN_countSuccessTrueAndServiceTag() {
        // GIVEN
        final ArgumentCaptor<String> metricCaptor = ArgumentCaptor.forClass(String.class);
        final ArgumentCaptor<Long> valueCaptor = ArgumentCaptor.forClass(Long.class);
        @SuppressWarnings("unchecked")
        final ArgumentCaptor<List<Tag>> tagsCaptor = ArgumentCaptor.forClass(List.class);

        // WHEN
        emitter.emitEndDatEmailSent(123L);

        // THEN
        verify(statsEmitter).count(metricCaptor.capture(), valueCaptor.capture(), tagsCaptor.capture());
        assertEquals(END_DATE_EMAIL_SENT, metricCaptor.getValue());
        assertEquals(1L, valueCaptor.getValue().longValue());

        final List<Tag> tags = tagsCaptor.getValue();
        assertTrue(tags.contains(new Tag(TAG_SEND_SUCCESS, Boolean.toString(true))));
        assertTrue(tags.contains(new Tag(TAG_SERVICE_NAME, END_DATE_EMAIL.name())));
    }

    @Test
    public void GIVEN_errorCause_WHEN_emitEndDateEmailFailed_THEN_countSuccessFalseAndErrorCause() {
        // GIVEN
        final ArgumentCaptor<String> metricCaptor = ArgumentCaptor.forClass(String.class);
        final ArgumentCaptor<Long> valueCaptor = ArgumentCaptor.forClass(Long.class);
        @SuppressWarnings("unchecked")
        final ArgumentCaptor<List<Tag>> tagsCaptor = ArgumentCaptor.forClass(List.class);

        // WHEN
        emitter.emitEndDateEmailFailed(ErrorCause.RAVEN_ERROR);

        // THEN
        verify(statsEmitter).count(metricCaptor.capture(), valueCaptor.capture(), tagsCaptor.capture());
        assertEquals(END_DATE_EMAIL_SENT, metricCaptor.getValue());
        assertEquals(1L, valueCaptor.getValue().longValue());

        final List<Tag> tags = tagsCaptor.getValue();
        assertTrue(tags.contains(new Tag(TAG_SEND_SUCCESS, Boolean.toString(false))));
        assertTrue(tags.contains(new Tag(TAG_SEND_ERROR_CAUSE, ErrorCause.RAVEN_ERROR.name())));
        assertTrue(tags.contains(new Tag(TAG_SERVICE_NAME, END_DATE_EMAIL.name())));
    }

    @Test
    public void GIVEN_serviceName_WHEN_emitCreditConfirmationEmailRequeued_THEN_countFailureWithServiceNameTag() {
        // GIVEN
        final ArgumentCaptor<String> metricCaptor = ArgumentCaptor.forClass(String.class);
        final ArgumentCaptor<Long> valueCaptor = ArgumentCaptor.forClass(Long.class);
        @SuppressWarnings("unchecked")
        final ArgumentCaptor<List<Tag>> tagsCaptor = ArgumentCaptor.forClass(List.class);

        // WHEN
        emitter.emitCreditConfirmationEmailRequeued(ServiceName.ONE_GRAPH);

        // THEN
        verify(statsEmitter).count(metricCaptor.capture(), valueCaptor.capture(), tagsCaptor.capture());
        assertEquals(END_DATE_SERVICE_FAILURE, metricCaptor.getValue());
        assertEquals(1L, valueCaptor.getValue().longValue());

        final List<Tag> tags = tagsCaptor.getValue();
        assertTrue(tags.contains(new Tag(TAG_SERVICE_NAME, ServiceName.ONE_GRAPH.name())));
    }
}
