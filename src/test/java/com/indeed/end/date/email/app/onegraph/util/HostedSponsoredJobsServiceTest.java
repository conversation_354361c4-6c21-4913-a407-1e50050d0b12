package com.indeed.end.date.email.app.onegraph.util;

import com.google.common.collect.ImmutableList;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.end.date.email.app.model.EndDateEmailJobData;
import com.indeed.end.date.email.app.onegraph.exception.ApolloQueryResolutionException;
import com.indeed.one.graph.client.HostedJobPostsQuery;
import com.indeed.util.core.time.WallClock;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.DateFormat;
import java.text.SimpleDateFormat;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

@ExtendWith(MockitoExtension.class)
public class HostedSponsoredJobsServiceTest {
    private static final DateFormat EXPECTED_END_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm");
    private static final int ADVERTISER_ID = 1;
    private static final int ATS_JOB_ID = 2;
    private static final long JOB_ID = 3;
    private static final String ENCRYPTED_JOBID = "encrypted_job_id";
    private static final String LOCATION = "location";
    private static final String COMPANY = "company";
    private static final String TITLE = "title";
    private static final String END_DATE = "2013-09-29T18:46:19Z";

    private HostedSponsoredJobsService hostedSponsoredJobsService;

    @Mock
    private ApolloResolver<HostedJobPostsQuery.Data> mockApolloResolver;

    @Mock
    private IDUtilService mockIdUtilService;

    @Mock
    private MetricsEmitter metricsEmitter;

    @Mock
    private WallClock wallClock;

    @BeforeEach
    public void setUp() {
        openMocks(this);
        lenient()
                .when(mockIdUtilService.atsJobIdToLegacyJobId(ATS_JOB_ID, ADVERTISER_ID))
                .thenReturn(ENCRYPTED_JOBID);
        hostedSponsoredJobsService =
                new HostedSponsoredJobsService(mockIdUtilService, mockApolloResolver, metricsEmitter, wallClock);
    }

    @Test
    public void GIVEN_freeJob_WHEN_getJobs_THEN_returnsValidJob() throws Exception {
        // GIVEN
        lenient().when(mockApolloResolver.resolveApolloQuery(any(), any())).thenReturn(mockResponseDataForFreeJob());
        final EndDateEmailJobData expectedJobData = EndDateEmailJobData.builder()
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(true)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .company(COMPANY)
                .title(TITLE)
                .build();

        // WHEN
        final EndDateEmailJobData jobData = hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertEquals(expectedJobData.getAdvertiserId(), jobData.getAdvertiserId());
        assertEquals(expectedJobData.getAtsJobId(), jobData.getAtsJobId());
        assertEquals(expectedJobData.isFreeJob(), jobData.isFreeJob());
        assertEquals(expectedJobData.getAdvertisingLocations(), jobData.getAdvertisingLocations());
        assertEquals(expectedJobData.getCompany(), jobData.getCompany());
        assertEquals(expectedJobData.getTitle(), jobData.getTitle());
        assertEquals(expectedJobData.getEndDate(), jobData.getEndDate());
    }

    @Test
    public void GIVEN_sponsoredJob_WHEN_getJob_THEN_returnsValidJob() throws Exception {
        // GIVEN
        lenient()
                .when(mockApolloResolver.resolveApolloQuery(any(), any()))
                .thenReturn(mockResponseDataForSponsoredJob());
        final EndDateEmailJobData expectedJobData = EndDateEmailJobData.builder()
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .endDate(EXPECTED_END_DATE_FORMAT.parse(END_DATE))
                .advertisingLocations(ImmutableList.of(LOCATION))
                .company(COMPANY)
                .title(TITLE)
                .build();

        // WHEN
        final EndDateEmailJobData jobData = hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertEquals(expectedJobData.getAdvertiserId(), jobData.getAdvertiserId());
        assertEquals(expectedJobData.getAtsJobId(), jobData.getAtsJobId());
        assertEquals(expectedJobData.isFreeJob(), jobData.isFreeJob());
        assertEquals(expectedJobData.getAdvertisingLocations(), jobData.getAdvertisingLocations());
        assertEquals(expectedJobData.getCompany(), jobData.getCompany());
        assertEquals(expectedJobData.getTitle(), jobData.getTitle());
        assertEquals(expectedJobData.getEndDate(), jobData.getEndDate());
    }

    @Test
    public void GIVEN_responseHasErrors_WHEN_getJob_THEN_returnNull() throws Exception {
        // GIVEN
        final String errorMessage = "error message";
        when(mockApolloResolver.resolveApolloQuery(any(), any()))
                .thenThrow(new ApolloQueryResolutionException(errorMessage));

        // WHEN

        // THEN
        assertThrows(Exception.class, () -> hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID));
    }

    @Test
    public void GIVEN_malformedEndDate_WHEN_getJob_THEN_returnsNull() throws Exception {
        // GIVEN: sponsored job with malformed end date string
        when(mockApolloResolver.resolveApolloQuery(any(), any()))
                .thenReturn(mockResponseDataForSponsoredJobWithMalformedEndDate());

        // WHEN
        final EndDateEmailJobData jobData = hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertNull(jobData);
    }

    @Test
    public void GIVEN_noResults_WHEN_getJob_THEN_returnsNull() throws Exception {
        // GIVEN
        when(mockApolloResolver.resolveApolloQuery(any(), any()))
                .thenReturn(new HostedJobPostsQuery.Data(
                        new HostedJobPostsQuery.HostedJobPostsByLegacyIds(ImmutableList.of())));

        // WHEN
        final EndDateEmailJobData jobData = hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertNull(jobData);
    }

    @Test
    public void GIVEN_periodicBudgetWithNullEndDate_WHEN_getJob_THEN_returnsJobWithNullEndDate() throws Exception {
        // GIVEN
        when(mockApolloResolver.resolveApolloQuery(any(), any()))
                .thenReturn(new HostedJobPostsQuery.Data(new HostedJobPostsQuery.HostedJobPostsByLegacyIds(
                        ImmutableList.of(new HostedJobPostsQuery.Result(new HostedJobPostsQuery.HostedJobPost(
                                COMPANY,
                                TITLE,
                                ImmutableList.of(new HostedJobPostsQuery.AdvertisingLocation(LOCATION, true)),
                                new HostedJobPostsQuery.HostedJobBudget(
                                        "",
                                        null,
                                        new HostedJobPostsQuery.OnPeriodicSponsoredJobBudget(
                                                String.valueOf(JOB_ID), null))))))));

        // WHEN
        final EndDateEmailJobData jobData = hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertFalse(jobData.isFreeJob());
        assertNull(jobData.getEndDate());
        assertEquals(ADVERTISER_ID, jobData.getAdvertiserId());
        assertEquals(ATS_JOB_ID, jobData.getAtsJobId());
        assertEquals(ImmutableList.of(LOCATION), jobData.getAdvertisingLocations());
        assertEquals(COMPANY, jobData.getCompany());
        assertEquals(TITLE, jobData.getTitle());
    }

    private HostedJobPostsQuery.Data mockResponseDataForSponsoredJob() {
        return new HostedJobPostsQuery.Data(new HostedJobPostsQuery.HostedJobPostsByLegacyIds(
                ImmutableList.of(new HostedJobPostsQuery.Result(new HostedJobPostsQuery.HostedJobPost(
                        COMPANY,
                        TITLE,
                        ImmutableList.of(new HostedJobPostsQuery.AdvertisingLocation(LOCATION, true)),
                        new HostedJobPostsQuery.HostedJobBudget(
                                "",
                                null,
                                new HostedJobPostsQuery.OnPeriodicSponsoredJobBudget(
                                        String.valueOf(JOB_ID), END_DATE)))))));
    }

    private HostedJobPostsQuery.Data mockResponseDataForFreeJob() {
        return new HostedJobPostsQuery.Data(new HostedJobPostsQuery.HostedJobPostsByLegacyIds(
                ImmutableList.of(new HostedJobPostsQuery.Result(new HostedJobPostsQuery.HostedJobPost(
                        COMPANY,
                        TITLE,
                        ImmutableList.of(new HostedJobPostsQuery.AdvertisingLocation(LOCATION, true)),
                        new HostedJobPostsQuery.HostedJobBudget(
                                "",
                                new HostedJobPostsQuery.OnFreeSponsoredJobBudget(String.valueOf(JOB_ID)),
                                null))))));
    }

    private HostedJobPostsQuery.Data mockResponseDataForSponsoredJobWithMalformedEndDate() {
        final String badEndDate = "not-a-date";
        return new HostedJobPostsQuery.Data(new HostedJobPostsQuery.HostedJobPostsByLegacyIds(
                ImmutableList.of(new HostedJobPostsQuery.Result(new HostedJobPostsQuery.HostedJobPost(
                        COMPANY,
                        TITLE,
                        ImmutableList.of(new HostedJobPostsQuery.AdvertisingLocation(LOCATION, true)),
                        new HostedJobPostsQuery.HostedJobBudget(
                                "",
                                null,
                                new HostedJobPostsQuery.OnPeriodicSponsoredJobBudget(
                                        String.valueOf(JOB_ID), badEndDate)))))));
    }
}
