package com.indeed.end.date.email.app.handler;

import com.google.common.collect.ImmutableList;
import com.indeed.adcentral.proto.events.EventProducerProto;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage.ActivityType;
import com.indeed.common.message.ConsumerResponse;
import com.indeed.common.message.Message;
import com.indeed.common.message.MessageProducer;
import com.indeed.end.date.email.app.adceventsdaemon.handler.AdCentralEventHandler;
import com.indeed.end.date.email.app.util.Constants;
import com.indeed.end.date.email.app.util.EndDateEmailStatsEmitter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.Clock;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AdCentralEventCallbackTest {

    private static final String UID = "uid-123";
    private static final ActivityType ACTIVITY_TYPE = ActivityType.PAUSE_JOB;

    @Mock
    private EventUIDCache eventUIDCache;

    @Mock
    private MessageProducer requeueProducer;

    @Mock
    private EndDateEmailStatsEmitter statsEmitter;

    @Mock
    private Clock clock;

    @Mock
    private EventProducerMessageParser parser;

    @Mock
    private AdCentralEventHandler handler;

    private AdCentralEventCallback callbackWithHandler;

    @BeforeEach
    public void setUp() {
        when(handler.getRegisteredActivityTypes()).thenReturn(ImmutableList.of(ACTIVITY_TYPE));
        callbackWithHandler = new AdCentralEventCallback(
                ImmutableList.of(handler), eventUIDCache, requeueProducer, statsEmitter, clock, parser);
    }

    private static EventProducerMessage buildEvent(final ActivityType type, final String uid) {
        final EventProducerProto.EventProducerMessage.Builder builder =
                EventProducerProto.EventProducerMessage.newBuilder().setAdvertiserId(1);
        if (type != null) {
            builder.setActivityType(type);
        }
        if (uid != null) {
            builder.setUid(uid);
        }
        return builder.build();
    }

    private static Message mockMessageWithPropertyStore() {
        final Message message = mock(Message.class);
        final Map<String, Object> properties = new HashMap<>();

        // Defaults
        properties.put(Constants.MESSAGE_NUMBER_OF_ATTEMPTS_PROPERTY_NAME, 0);

        lenient()
                .doAnswer(invocation -> {
                    final String key = invocation.getArgument(0);
                    final int defaultVal = invocation.getArgument(1);
                    return properties.getOrDefault(key, defaultVal);
                })
                .when(message)
                .getIntegerProperty(any(String.class), any(Integer.class));

        lenient()
                .doAnswer(invocation -> {
                    final String key = invocation.getArgument(0);
                    final boolean defaultVal = invocation.getArgument(1);
                    return properties.getOrDefault(key, defaultVal);
                })
                .when(message)
                .getBooleanProperty(any(String.class), any(Boolean.class));

        lenient()
                .doAnswer(invocation -> {
                    final String key = invocation.getArgument(0);
                    final Integer value = invocation.getArgument(1);
                    properties.put(key, value);
                    return null;
                })
                .when(message)
                .setIntegerProperty(any(String.class), any(Integer.class));

        lenient()
                .doAnswer(invocation -> {
                    final String key = invocation.getArgument(0);
                    final Boolean value = invocation.getArgument(1);
                    properties.put(key, value);
                    return null;
                })
                .when(message)
                .setBooleanProperty(any(String.class), any(Boolean.class));

        // ack/nack are void
        doNothing().when(message).ack();
        doNothing().when(message).nack(any(Boolean.class));

        return message;
    }

    @Test
    public void GIVEN_invalidEvent_WHEN_onMessage_THEN_ackAndLogInvalid() {
        // GIVEN
        final Message message = mockMessageWithPropertyStore();
        when(parser.parseMessage(message)).thenReturn(null);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        verify(message, times(1)).ack();
        verify(statsEmitter, times(1)).logInvalid();
    }

    @Test
    public void GIVEN_validEvent_AND_handlerAck_WHEN_onMessage_THEN_ackAndLogSuccess() {
        // GIVEN
        final Message message = mockMessageWithPropertyStore();
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 150L);
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.ACK);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        verify(statsEmitter).logTime(ACTIVITY_TYPE, 50L);
        verify(statsEmitter).logSuccess(ACTIVITY_TYPE);
        verify(statsEmitter).logNumberOfRetries(ACTIVITY_TYPE, 0);
        verify(message).ack();
        verify(eventUIDCache).safeAddUIDToProcessedCache(UID);
        verify(eventUIDCache).safeRemoveFromProcessingCache(UID);
    }

    @Test
    public void GIVEN_handlerNack_WHEN_onMessage_THEN_nackAndLogFailure() {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 120L);
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.NACK);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        verify(message).nack(false);
        verify(statsEmitter).logFailure(ACTIVITY_TYPE);
        verifyNoMoreInteractions(requeueProducer);
    }

    @Test
    public void GIVEN_ackAfterFirstRequeue_WHEN_onMessage_THEN_retryOnceAndAck() {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 150L, 200L, 260L);
        when(eventUIDCache.isUIDInProcessedCache(UID))
                .thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE, EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event))
                .thenReturn(ConsumerResponse.NACK_WITH_REQUEUE)
                .thenReturn(ConsumerResponse.ACK);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        // After first failure with requeue, retry once (since max retries = 1) then succeed
        final InOrder inOrder = inOrder(statsEmitter);
        inOrder.verify(statsEmitter).logTime(ACTIVITY_TYPE, 50L);
        inOrder.verify(statsEmitter).logTime(ACTIVITY_TYPE, 60L);

        verify(statsEmitter).logSuccess(ACTIVITY_TYPE);
        // Should be marked as success after retry
        verify(statsEmitter).logSuccessAfterRetry(ACTIVITY_TYPE);
        // Attempts should be 1 when success is recorded
        verify(statsEmitter).logNumberOfRetries(ACTIVITY_TYPE, 1);
        verify(message).ack();

        // UID cache interactions twice for two attempts
        verify(eventUIDCache, times(2)).safeAddUIDToProcessingCache(UID);
        verify(eventUIDCache, times(2)).safeRemoveFromProcessingCache(UID);
        // Added to processed only on success
        verify(eventUIDCache, times(1)).safeAddUIDToProcessedCache(UID);

        // Should not send to delay queue
        verifyNoInteractions(requeueProducer);
    }

    @Test
    public void GIVEN_retryExhausted_AND_requeueResponse_WHEN_onMessage_THEN_sendToDelayQueue() throws Exception {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        // Start with 1 attempt already
        message.setIntegerProperty(Constants.MESSAGE_NUMBER_OF_ATTEMPTS_PROPERTY_NAME, 1);

        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 110L);
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.NACK_WITH_REQUEUE);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        // Should be sent to delay queue, not acked or nacked here
        verify(requeueProducer, times(1)).send(message);
        verifyNoMoreInteractions(requeueProducer);
        verify(message, times(0)).ack();
        verify(message, times(0)).nack(false);
    }

    @Test
    public void GIVEN_missingActivityType_WHEN_handle_THEN_returnNack() {
        // GIVEN:
        final EventProducerMessage eventWithoutType =
                EventProducerMessage.newBuilder().setUid(UID).setAdvertiserId(1).build();

        final AdCentralEventCallback callbackNoHandlers = new AdCentralEventCallback(
                ImmutableList.of(), eventUIDCache, requeueProducer, statsEmitter, clock, parser);

        // WHEN
        final ConsumerResponse response = callbackNoHandlers.handle(eventWithoutType.toByteArray());

        // THEN
        assertEquals(ConsumerResponse.NACK, response);
    }

    @Test
    public void GIVEN_missingHandler_WHEN_handle_THEN_ack() {
        // GIVEN:
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);

        final AdCentralEventCallback callbackNoHandlers = new AdCentralEventCallback(
                ImmutableList.of(), eventUIDCache, requeueProducer, statsEmitter, clock, parser);

        // WHEN
        final ConsumerResponse response = callbackNoHandlers.handle(event.toByteArray());

        // THEN
        assertEquals(ConsumerResponse.ACK, response);
    }

    @Test
    public void GIVEN_duplicateRegistration_WHEN_registerHandler_THEN_throw() {
        // GIVEN:
        final AdCentralEventCallback callback = new AdCentralEventCallback(
                ImmutableList.of(), eventUIDCache, requeueProducer, statsEmitter, clock, parser);

        final AdCentralEventHandler handler1 = mock(AdCentralEventHandler.class);
        final AdCentralEventHandler handler2 = mock(AdCentralEventHandler.class);

        // WHEN
        callback.registerHandler(ACTIVITY_TYPE, handler1);

        // THEN
        assertThrows(RuntimeException.class, () -> callback.registerHandler(ACTIVITY_TYPE, handler2));
    }

    @Test
    public void GIVEN_messageRequeuedProperty_WHEN_onMessageTHEN_logSuccessAfterRequeue() {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        // Mark message as previously requeued
        message.setBooleanProperty(Constants.MESSAGE_IS_REQUEUED_PROPERTY_NAME, true);

        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 150L);
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.ACK);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        verify(statsEmitter).logSuccess(ACTIVITY_TYPE);
        verify(statsEmitter).logSuccessAfterRequeue(ACTIVITY_TYPE);
        verify(statsEmitter).logNumberOfRetries(ACTIVITY_TYPE, 0);
        verify(message).ack();
    }

    @Test
    public void GIVEN_handlerNackAsFailure_WHEN_onMessage_THEN_nackAndLogFailure() {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 120L);
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.NACK_AS_FAILURE);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        verify(message).nack(false);
        verify(statsEmitter).logFailure(ACTIVITY_TYPE);
    }

    @Test
    public void GIVEN_handlerRequeueAsFailure_WHEN_onMessage_THEN_sendToDelayQueue() throws Exception {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        // Exhaust initial retry so we go to delay queue branch
        message.setIntegerProperty(Constants.MESSAGE_NUMBER_OF_ATTEMPTS_PROPERTY_NAME, 1);

        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 130L);
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.NACK_WITH_REQUEUE_AS_FAILURE);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        verify(requeueProducer, times(1)).send(message);
        verify(message, times(0)).ack();
        verify(message, times(0)).nack(false);
    }

    @Test
    public void GIVEN_requeueSendThrows_WHEN_onMessage_THEN_revertFlagAndNack() throws Exception {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        // Exhaust initial retry so go to delay queue branch
        message.setIntegerProperty(Constants.MESSAGE_NUMBER_OF_ATTEMPTS_PROPERTY_NAME, 1);
        // Initially not marked as requeued
        message.setBooleanProperty(Constants.MESSAGE_IS_REQUEUED_PROPERTY_NAME, false);

        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 110L);
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.NACK_WITH_REQUEUE);

        doThrow(new RuntimeException("send failure")).when(requeueProducer).send(message);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        // Flag should be reverted to original value (false)
        assertFalse(message.getBooleanProperty(Constants.MESSAGE_IS_REQUEUED_PROPERTY_NAME, false));
        verify(message).nack(false);
        verify(statsEmitter).logFailure(ACTIVITY_TYPE);
    }

    @Test
    public void GIVEN_exceedsMaxRequeues_WHEN_onMessage_THEN_nackAndLogFailure() {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        // Attempts exceed retries + requeues (1 + 30)
        message.setIntegerProperty(Constants.MESSAGE_NUMBER_OF_ATTEMPTS_PROPERTY_NAME, 31);

        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L, 110L);
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.NACK_WITH_REQUEUE);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        verify(message).nack(false);
        verify(statsEmitter).logFailure(ACTIVITY_TYPE);
        verifyNoMoreInteractions(requeueProducer);
    }

    @Test
    public void GIVEN_uidInProcessedCache_WHEN_handle_THEN_ack() {
        // GIVEN:
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);

        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.IN_CACHE);

        // WHEN
        final ConsumerResponse response = callbackWithHandler.handle(event.toByteArray());
        assertEquals(ConsumerResponse.ACK, response);

        // THEN
        verify(eventUIDCache, times(0)).safeAddUIDToProcessingCache(any());
    }

    @Test
    public void GIVEN_processedCacheError_WHEN_handle_THEN_requeue() {
        // GIVEN:
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);

        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.ERROR);

        // WHEN
        final ConsumerResponse response = callbackWithHandler.handle(event.toByteArray());

        // THEN
        assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
    }

    @Test
    public void GIVEN_processingCacheAddFailed_WHEN_handle_THEN_requeue() {
        // GIVEN:
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);

        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ALREADY_IN_CACHE);

        // WHEN
        final ConsumerResponse response = callbackWithHandler.handle(event.toByteArray());

        // THEN
        assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
    }

    @Test
    public void GIVEN_handlerThrowsInvalidEventProducerMessage_WHEN_handle_THEN_setProcessedAndNack() {
        // GIVEN:
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);

        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        doThrow(new com.indeed.end.date.email.app.exception.InvalidEventProducerMessageException("bad"))
                .when(handler)
                .handleEvent(event);

        // WHEN
        final ConsumerResponse response = callbackWithHandler.handle(event.toByteArray());
        assertEquals(ConsumerResponse.NACK, response);

        // THEN
        verify(eventUIDCache).safeAddUIDToProcessedCache(UID);
        verify(eventUIDCache).safeRemoveFromProcessingCache(UID);
    }

    @Test
    public void GIVEN_handlerThrows_WHEN_handle_THEN_removeFromProcessingAndRequeue() {
        // GIVEN:
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);

        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        doThrow(new RuntimeException("unexpected")).when(handler).handleEvent(event);

        // WHEN
        final ConsumerResponse response = callbackWithHandler.handle(event.toByteArray());

        // THEN
        assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
        verify(eventUIDCache, times(0)).safeAddUIDToProcessedCache(UID);
        verify(eventUIDCache).safeRemoveFromProcessingCache(UID);
    }

    @Test
    public void GIVEN_logTimeThrows_WHEN_onMessage_THEN_stillProcessResponse() {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);
        when(parser.parseMessage(message)).thenReturn(event);

        when(clock.millis()).thenReturn(100L).thenThrow(new RuntimeException());
        when(eventUIDCache.isUIDInProcessedCache(UID)).thenReturn(EventUIDCache.GetCacheStatus.NOT_IN_CACHE);
        when(eventUIDCache.safeAddUIDToProcessingCache(UID)).thenReturn(EventUIDCache.AddCacheStatus.ADDED_TO_CACHE);
        when(handler.handleEvent(event)).thenReturn(ConsumerResponse.ACK);

        // WHEN
        callbackWithHandler.onMessage(message);

        // THEN
        verify(statsEmitter).logSuccess(ACTIVITY_TYPE);

        // Even though logTime threw, still process the response and ack
        verify(message).ack();
    }

    @Test
    public void GIVEN_nullResponse_WHEN_processResponse_THEN_nackAndLogFailure() {
        // GIVEN:
        final Message message = mockMessageWithPropertyStore();
        final EventProducerMessage event = buildEvent(ACTIVITY_TYPE, UID);

        // WHEN
        callbackWithHandler.processResponse(message, null, event);

        // THEN
        verify(message).nack(false);
        verify(statsEmitter).logFailure(ACTIVITY_TYPE);
    }

    @Test
    public void GIVEN_eventWithoutUid_WHEN_handle_THEN_doNotTouchUidCachesAndAck() {
        // GIVEN:
        final EventProducerMessage eventNoUid = buildEvent(ACTIVITY_TYPE, null);

        // Let handler succeed
        when(handler.handleEvent(eventNoUid)).thenReturn(ConsumerResponse.ACK);

        // WHEN
        final ConsumerResponse response = callbackWithHandler.handle(eventNoUid.toByteArray());

        // THEN
        assertEquals(ConsumerResponse.ACK, response);
        verify(eventUIDCache, times(0)).safeAddUIDToProcessingCache(any());
        verify(eventUIDCache, times(0)).safeAddUIDToProcessedCache(any());
        verify(eventUIDCache, times(0)).safeRemoveFromProcessingCache(any());
    }

    @Test
    public void GIVEN_properActivityHandlers_WHEN_close_THEN_closeWithoutException() throws Exception {
        // GIVEN: No exception case

        // WHEN
        callbackWithHandler.close();

        // THEN
        verify(handler, times(1)).close();
    }

    @Test
    public void GIVEN_handlerCloseThrows_WHEN_close_THEN_doNothing() throws Exception {
        // GIVEN: Exception case
        org.mockito.Mockito.reset(handler);
        doThrow(new RuntimeException("close fail")).when(handler).close();

        // WHEN
        callbackWithHandler.close();

        // THEN
        verify(handler, times(1)).close();
    }

    @Test
    public void GIVEN_invalidBytes_WHEN_handle_THEN_logInvalidAndAck() {
        // GIVEN:
        // Using bytes that produce a truncated varint to force InvalidProtocolBufferException
        final byte[] invalid = new byte[] {(byte) 0x08};

        final AdCentralEventCallback callbackNoHandlers = new AdCentralEventCallback(
                ImmutableList.of(), eventUIDCache, requeueProducer, statsEmitter, clock, parser);

        // WHEN
        final ConsumerResponse response = callbackNoHandlers.handle(invalid);

        // THEN
        assertEquals(ConsumerResponse.ACK, response);
        verify(statsEmitter).logInvalid();
    }
}
