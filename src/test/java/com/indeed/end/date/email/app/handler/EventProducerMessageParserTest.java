package com.indeed.end.date.email.app.handler;

import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage.ActivityType;
import com.indeed.common.message.Message;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EventProducerMessageParserTest {

    @Mock
    private Message message;

    private final EventProducerMessageParser parser = new EventProducerMessageParser();

    @Test
    public void GIVEN_validBytes_WHEN_parseMessage_THEN_returnEvent() {
        // GIVEN
        final EventProducerMessage original = EventProducerMessage.newBuilder()
                .setAdvertiserId(123)
                .setActivityType(ActivityType.PAUSE_JOB)
                .build();
        when(message.getBody()).thenReturn(original.toByteArray());

        // WHEN
        final EventProducerMessage parsed = parser.parseMessage(message);

        // THEN
        assertNotNull(parsed);
        assertEquals(original, parsed);
        assertEquals(123, parsed.getAdvertiserId());
        assertEquals(ActivityType.PAUSE_JOB, parsed.getActivityType());
    }

    @Test
    public void GIVEN_invalidBytes_WHEN_parseMessage_THEN_returnNull() {
        // GIVEN
        when(message.getBody()).thenReturn(new byte[] {0x00, 0x01, 0x02});

        // WHEN
        final EventProducerMessage parsed = parser.parseMessage(message);

        // THEN
        assertNull(parsed);
    }
}
