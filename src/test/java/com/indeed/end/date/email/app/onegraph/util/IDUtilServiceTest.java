package com.indeed.end.date.email.app.onegraph.util;

import com.indeed.dradis.common.encryption.EncryptionUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

@ExtendWith(MockitoExtension.class)
public class IDUtilServiceTest {
    @Mock
    private UUID mockUUID;

    @InjectMocks
    IDUtilService idUtilService;

    private MockedStatic<EncryptionUtil> encryptionUtilMockedStatic;
    private MockedStatic<UUID> uuidMockedStatic;

    @BeforeEach
    public void setup() {
        openMocks(this);
        encryptionUtilMockedStatic = Mockito.mockStatic(EncryptionUtil.class);
        uuidMockedStatic = Mockito.mockStatic(UUID.class);
    }

    @AfterEach
    public void teardown() {
        encryptionUtilMockedStatic.close();
        uuidMockedStatic.close();
    }

    @Test
    public void GIVEN_ids_WHEN_atsJobIdToLegacyJobId_THEN_returnsLegacyJobId() {
        // GIVEN
        final int advertiserId = 1;
        final int atsJobId = 2;
        final String legacyID = "id";
        when(EncryptionUtil.safeEncode(atsJobId, advertiserId, EncryptionUtil.EncryptionType.JOB_ID))
                .thenReturn(legacyID);

        // WHEN
        final String id = idUtilService.atsJobIdToLegacyJobId(atsJobId, advertiserId);

        // THEN
        assertEquals(legacyID, id);
    }

    @Test
    public void GIVEN_nothing_WHEN_getUUID_THEN_returnsUUID() {
        // GIVEN
        final String uuid = "196d804a-d264-4213-bd3f-5df161607943";
        Mockito.when(mockUUID.toString()).thenReturn(uuid);
        uuidMockedStatic.when(UUID::randomUUID).thenReturn(mockUUID);

        // WHEN
        final String id = idUtilService.getUUID();

        // THEN
        assertEquals(uuid, id);
    }
}
