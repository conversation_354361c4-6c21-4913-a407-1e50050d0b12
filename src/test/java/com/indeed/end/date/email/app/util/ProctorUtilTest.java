package com.indeed.end.date.email.app.util;

import com.indeed.end.date.email.app.groups.EndDateEmailAppGroupsManager;
import com.indeed.end.date.email.app.groups.EndDateEmailAppProctorGroups;
import com.indeed.proctor.common.Identifiers;
import com.indeed.proctor.common.ProctorResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ProctorUtilTest {

    private static final int ADVERTISER_ID = 42;

    @Mock
    private EndDateEmailAppGroupsManager groupsManager;

    private ProctorUtil proctorUtil;

    @BeforeEach
    public void setUp() {
        proctorUtil = new ProctorUtil(groupsManager);
    }

    @Test
    public void GIVEN_validInputs_WHEN_getProctorGroups_THEN_mapArgsAndIdentifiers() {
        // GIVEN
        when(groupsManager.determineBuckets(any(), any(Integer.class), any(), any(), any(), any(), any()))
                .thenReturn(ProctorResult.EMPTY);

        final String hl = "fr_CA";
        final String co = "CA";
        final String claim = "claimX";

        final ArgumentCaptor<Identifiers> idsCaptor = ArgumentCaptor.forClass(Identifiers.class);
        final ArgumentCaptor<Integer> advIdCaptor = ArgumentCaptor.forClass(Integer.class);
        final ArgumentCaptor<String> hlCaptor = ArgumentCaptor.forClass(String.class);
        final ArgumentCaptor<String> langCaptor = ArgumentCaptor.forClass(String.class);
        final ArgumentCaptor<String> coCaptor1 = ArgumentCaptor.forClass(String.class);
        final ArgumentCaptor<String> coCaptor2 = ArgumentCaptor.forClass(String.class);
        final ArgumentCaptor<String> claimCaptor = ArgumentCaptor.forClass(String.class);

        // WHEN
        final EndDateEmailAppProctorGroups result = proctorUtil.getProctorGroups(ADVERTISER_ID, hl, co, claim);

        // THEN
        assertNotNull(result);

        verify(groupsManager)
                .determineBuckets(
                        idsCaptor.capture(),
                        advIdCaptor.capture(),
                        hlCaptor.capture(),
                        langCaptor.capture(),
                        coCaptor1.capture(),
                        coCaptor2.capture(),
                        claimCaptor.capture());

        // identifiers object should be constructed
        final Identifiers identifiers = idsCaptor.getValue();
        assertNotNull(identifiers);

        assertEquals(Integer.valueOf(ADVERTISER_ID), advIdCaptor.getValue());
        assertEquals(hl, hlCaptor.getValue());
        assertEquals("fr", langCaptor.getValue());
        assertEquals(co, coCaptor1.getValue());
        assertEquals(co, coCaptor2.getValue());
        assertEquals(claim, claimCaptor.getValue());
    }

    @Test
    public void GIVEN_nullHl_WHEN_getProctorGroups_THEN_languageNull() {
        // GIVEN
        final ArgumentCaptor<String> langCaptor = ArgumentCaptor.forClass(String.class);

        when(groupsManager.determineBuckets(any(), any(Integer.class), any(), any(), any(), any(), any()))
                .thenReturn(ProctorResult.EMPTY);

        final String co = "US";

        // WHEN
        final EndDateEmailAppProctorGroups result = proctorUtil.getProctorGroups(ADVERTISER_ID, null, co, "");

        // THEN
        assertNotNull(result);

        verify(groupsManager)
                .determineBuckets(
                        any(Identifiers.class),
                        any(Integer.class),
                        org.mockito.ArgumentMatchers.<String>isNull(),
                        langCaptor.capture(),
                        any(String.class),
                        any(String.class),
                        any(String.class));
        assertNull(langCaptor.getValue());
    }

    @Test
    public void GIVEN_managerThrows_WHEN_getProctorGroups_THEN_returnFallbackGroups() {
        // GIVEN
        doThrow(new RuntimeException())
                .when(groupsManager)
                .determineBuckets(any(), any(Integer.class), any(), any(), any(), any(), any());

        // WHEN
        final EndDateEmailAppProctorGroups result = proctorUtil.getProctorGroups(ADVERTISER_ID, "en", "US", "claim");

        // THEN
        assertNotNull(result); // non-null ensures constructor path executed after exception.
    }

    @Test
    public void GIVEN_overload_WHEN_getProctorGroups_THEN_setEmptyClaimCode() {
        // GIVEN
        final ArgumentCaptor<String> claimCaptor = ArgumentCaptor.forClass(String.class);

        when(groupsManager.determineBuckets(any(), any(Integer.class), any(), any(), any(), any(), any()))
                .thenReturn(ProctorResult.EMPTY);

        // WHEN
        proctorUtil.getProctorGroups(ADVERTISER_ID, "en", "US");

        // THEN
        verify(groupsManager)
                .determineBuckets(
                        any(Identifiers.class),
                        any(Integer.class),
                        any(String.class),
                        any(String.class),
                        any(String.class),
                        any(String.class),
                        claimCaptor.capture());
        assertEquals("", claimCaptor.getValue());
    }
}
