package com.indeed.end.date.email.app.onegraph.util;

import com.apollographql.apollo3.ApolloCall;
import com.apollographql.apollo3.ApolloClient;
import com.apollographql.apollo3.api.ApolloResponse;
import com.apollographql.apollo3.api.Operation;
import com.apollographql.apollo3.api.Query;
import com.apollographql.apollo3.rx3.Rx3Apollo;
import com.indeed.end.date.email.app.model.HttpHeader;
import io.reactivex.rxjava3.core.Single;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
public class ApolloResolverTest {

    private interface DummyData extends Operation.Data, Query.Data {}

    @Mock
    private ApolloClient.Builder builder;

    @Mock
    private ApolloClient.Builder builderCopy;

    @Mock
    private ApolloClient apolloClient;

    @Mock
    private ApolloCall<DummyData> apolloCall;

    @Mock
    private Query<DummyData> query;

    @Test
    public void GIVEN_headers_WHEN_resolveApolloQuery_THEN_addHeaders() {
        // GIVEN
        final ApolloResolver<DummyData> resolver = new ApolloResolver<>(builder);

        final List<HttpHeader> headers = List.of(new HttpHeader("h1", "v1"), new HttpHeader("h2", "v2"));

        when(builder.copy()).thenReturn(builderCopy);
        when(builderCopy.addHttpHeader("h1", "v1")).thenReturn(builderCopy);
        when(builderCopy.addHttpHeader("h2", "v2")).thenReturn(builderCopy);
        when(builderCopy.build()).thenReturn(apolloClient);
        when(apolloClient.query(any(Query.class))).thenReturn(apolloCall);

        final ApolloResponse<DummyData> response = mock(ApolloResponse.class);
        when(response.hasErrors()).thenReturn(false);

        try (final MockedStatic<Rx3Apollo> staticMock = mockStatic(Rx3Apollo.class)) {
            staticMock.when(() -> Rx3Apollo.single(apolloCall)).thenReturn(Single.just(response));

            // WHEN
            resolver.resolveApolloQuery(query, headers);

            // THEN
            verify(builder, times(1)).copy();
            verify(builderCopy, times(1)).addHttpHeader("h1", "v1");
            verify(builderCopy, times(1)).addHttpHeader("h2", "v2");
            verify(builderCopy, times(1)).build();
            verify(apolloClient, times(1)).query(query);
            verify(apolloClient, times(1)).close();
        }
    }

    @Test
    public void GIVEN_rxThrows_WHEN_resolveApolloQuery_THEN_closeClient() {
        // GIVEN
        final ApolloResolver<DummyData> resolver = new ApolloResolver<>(builder);
        final List<HttpHeader> headers = List.of();

        when(builder.copy()).thenReturn(builderCopy);
        when(builderCopy.build()).thenReturn(apolloClient);
        when(apolloClient.query(any(Query.class))).thenReturn(apolloCall);

        try (final MockedStatic<Rx3Apollo> staticMock = mockStatic(Rx3Apollo.class)) {
            staticMock.when(() -> Rx3Apollo.single(apolloCall)).thenThrow(new RuntimeException());

            // WHEN
            assertThrows(RuntimeException.class, () -> resolver.resolveApolloQuery(query, headers));

            // THEN
            verify(apolloClient, times(1)).close();
        }
    }

    @Test
    public void GIVEN_responseHasErrors_WHEN_resolveApolloQuery_THEN_throwAndClose() {
        // GIVEN
        final ApolloResolver<DummyData> resolver = new ApolloResolver<>(builder);

        when(builder.copy()).thenReturn(builderCopy);
        when(builderCopy.build()).thenReturn(apolloClient);
        when(apolloClient.query(any(Query.class))).thenReturn((ApolloCall) apolloCall);

        final ApolloResponse<DummyData> response = mock(ApolloResponse.class, invocation -> {
            final String name = invocation.getMethod().getName();
            if ("hasErrors".equals(name)) {
                return true;
            }
            if ("getErrors".equals(name)) {
                return java.util.List.of(mock(com.apollographql.apollo3.api.Error.class));
            }
            return null;
        });
        // Ensure explicit stubbing is present as well
        when(response.hasErrors()).thenReturn(true);
        try {
            Class<?> clazz = response.getClass();
            boolean set = false;
            while (clazz != null && !set) {
                try {
                    final java.lang.reflect.Field errorsField = clazz.getDeclaredField("errors");
                    errorsField.setAccessible(true);
                    errorsField.set(response, java.util.List.of(mock(com.apollographql.apollo3.api.Error.class)));
                    set = true;
                } catch (NoSuchFieldException ignore) {
                    clazz = clazz.getSuperclass();
                }
            }
        } catch (Exception ignored) {
            // best-effort; if reflection fails, the default answer above should still provide getErrors()
        }

        try (final MockedStatic<Rx3Apollo> staticMock = mockStatic(Rx3Apollo.class)) {
            staticMock
                    .when(() -> Rx3Apollo.single(apolloCall))
                    .thenReturn(io.reactivex.rxjava3.core.Single.just(response));

            // WHEN
            assertThrows(Throwable.class, () -> resolver.resolveApolloQuery(query, java.util.List.of()));

            // THEN
            verify(apolloClient, times(1)).close();
        }
    }

    @Test
    public void GIVEN_responseErrorsNull_WHEN_resolveApolloQuery_THEN_assertionFailsAndClose() {
        // GIVEN
        final ApolloResolver<DummyData> resolver = new ApolloResolver<>(builder);

        when(builder.copy()).thenReturn(builderCopy);
        when(builderCopy.build()).thenReturn(apolloClient);
        when(apolloClient.query(any(Query.class))).thenReturn((ApolloCall) apolloCall);

        final ApolloResponse<DummyData> response = mock(ApolloResponse.class);
        when(response.hasErrors()).thenReturn(true);
        // Do not set errors list -> remains null to trigger assertion failure

        try (final MockedStatic<Rx3Apollo> staticMock = mockStatic(Rx3Apollo.class)) {
            staticMock
                    .when(() -> Rx3Apollo.single(apolloCall))
                    .thenReturn(io.reactivex.rxjava3.core.Single.just(response));

            // WHEN / THEN
            assertThrows(AssertionError.class, () -> resolver.resolveApolloQuery(query, java.util.List.of()));
            verify(apolloClient, times(1)).close();
        }
    }
}
