package com.indeed.end.date.email.app.util;

import com.google.common.collect.ImmutableList;
import com.indeed.advertiserservice.client.AdvertiserService;
import com.indeed.advertiserservice.client.AdvertiserServiceRpcClientException;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.Advertiser;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.AdvertiserProperty;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.BillingAddress;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetAdvertiserPropertyRequest;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetAdvertiserPropertyResponse;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetAdvertisersRequest;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetAdvertisersResponse;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetBillingAddressRequest;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.GetBillingAddressResponse;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.PropertyName;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.util.core.time.WallClock;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestAdvertiserUtil {

    private static final int ADVERTISER_ID = 1234;
    private static final int INVALID_ID = 0;
    private static final String LOCALE = "en_GB";
    private static final String BILLING_COUNTRY = "GB";
    private static final String ADVERTISER_NUMBER = "advNum";
    private static final List<PropertyName> PROPERTY_NAMES =
            ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS);

    @Mock
    private AdvertiserService advertiserService;

    @Mock
    private MetricsEmitter metricsEmitter;

    @Mock
    private WallClock wallClock;

    @InjectMocks
    private AdvertiserUtil advertiserUtil;

    // ------ getBillingCountryForAdvertiserId ------

    @Test
    public void testGetBillingCountryForAdvertiserId() throws AdvertiserServiceRpcClientException {
        when(advertiserService.getBillingAddresses(getDefaultBillingAddressRequest()))
                .thenReturn(getDefaultBillingAddressResponse(
                        BillingAddress.newBuilder().setCountry(BILLING_COUNTRY).build()));

        final Optional<String> result = advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID);
        assertTrue(result.isPresent());
        assertEquals(BILLING_COUNTRY, result.get());
    }

    @Test
    public void testGetBillingCountryForAccountId_invalidAdvertiserId() {
        final Optional<String> result = advertiserUtil.getBillingCountryForAdvertiserId(INVALID_ID);
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetBillingCountryForAdvertiserId_advertiserServiceException()
            throws AdvertiserServiceRpcClientException {
        when(advertiserService.getBillingAddresses(getDefaultBillingAddressRequest()))
                .thenThrow(new AdvertiserServiceRpcClientException("failed"));

        final Optional<String> result = advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID);
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetBillingCountryForAccountId_missingBillingAddress() throws AdvertiserServiceRpcClientException {
        when(advertiserService.getBillingAddresses(getDefaultBillingAddressRequest()))
                .thenReturn(GetBillingAddressResponse.getDefaultInstance());

        final Optional<String> result = advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID);
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetBillingCountryForAccountId_missingCountry() throws AdvertiserServiceRpcClientException {
        when(advertiserService.getBillingAddresses(getDefaultBillingAddressRequest()))
                .thenReturn(getDefaultBillingAddressResponse(BillingAddress.getDefaultInstance()));

        final Optional<String> result = advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID);
        assertFalse(result.isPresent());
    }

    // ------ getAdvertiserPropertiesUnsafe ------

    @Test
    public void testGetAdvertiserPropertiesUnsafe() throws AdvertiserServiceRpcClientException {
        final GetAdvertiserPropertyRequest request = GetAdvertiserPropertyRequest.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .addPropertyName(PROPERTY_NAMES.get(0))
                .addPropertyName(PROPERTY_NAMES.get(1))
                .build();
        final GetAdvertiserPropertyResponse response = GetAdvertiserPropertyResponse.newBuilder()
                .addAdvertiserProperty(AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue("en_US"))
                .build();

        when(advertiserService.getAdvertiserProperties(request)).thenReturn(response);

        final List<AdvertiserProperty> results =
                advertiserUtil.getAdvertiserPropertiesUnsafe(ADVERTISER_ID, PROPERTY_NAMES);
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(PropertyName.LOCALE, results.get(0).getPropertyName());
        assertEquals("en_US", results.get(0).getPropertyValue());
    }

    @Test
    public void testGetAdvertiserPropertiesUnsafe_invalidAdvertiserId() throws AdvertiserServiceRpcClientException {
        final List<AdvertiserProperty> results = advertiserUtil.getAdvertiserPropertiesUnsafe(0, PROPERTY_NAMES);
        assertEquals(Collections.emptyList(), results);
    }

    @Test
    public void testGetAdvertiserPropertiesUnsafe_nullPropertiesList() throws AdvertiserServiceRpcClientException {
        final List<AdvertiserProperty> results = advertiserUtil.getAdvertiserPropertiesUnsafe(ADVERTISER_ID, null);
        assertEquals(Collections.emptyList(), results);
    }

    @Test
    public void testGetAdvertiserPropertiesUnsafe_emptyPropertiesList() throws AdvertiserServiceRpcClientException {
        final List<AdvertiserProperty> results =
                advertiserUtil.getAdvertiserPropertiesUnsafe(ADVERTISER_ID, Collections.emptyList());
        assertEquals(Collections.emptyList(), results);
    }

    @Test
    public void testGetAdvertiserFromAdvertiserIdUnsafe() throws AdvertiserServiceRpcClientException {
        final GetAdvertisersRequest request = GetAdvertisersRequest.newBuilder()
                .addAdvertiserId(ADVERTISER_ID)
                .build();
        final GetAdvertisersResponse response = GetAdvertisersResponse.newBuilder()
                .addAdvertiser(Advertiser.getDefaultInstance())
                .build();

        when(advertiserService.getAdvertisers(request)).thenReturn(response);

        final Optional<Advertiser> result = advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID);
        assertTrue(result.isPresent());
        assertEquals(Advertiser.getDefaultInstance(), result.get());
    }

    @Test
    public void testGetAdvertiserFromAdvertiserIdUnsafe_invalidAdvertiserId()
            throws AdvertiserServiceRpcClientException {
        final Optional<Advertiser> result = advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(0);
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetAdvertiserFromAdvertiserIdUnsafe_missingAdvertiser() throws AdvertiserServiceRpcClientException {
        final GetAdvertisersRequest request = GetAdvertisersRequest.newBuilder()
                .addAdvertiserId(ADVERTISER_ID)
                .build();

        when(advertiserService.getAdvertisers(request)).thenReturn(GetAdvertisersResponse.getDefaultInstance());

        final Optional<Advertiser> result = advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID);
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetAdvertiserFromAdvertiserIdUnsafe_nullAdvertiser() throws AdvertiserServiceRpcClientException {
        final GetAdvertisersRequest request = GetAdvertisersRequest.newBuilder()
                .addAdvertiserId(ADVERTISER_ID)
                .build();

        when(advertiserService.getAdvertisers(request)).thenReturn(null);

        final Optional<Advertiser> result = advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID);
        assertFalse(result.isPresent());
    }

    private GetBillingAddressRequest getDefaultBillingAddressRequest() {
        return GetBillingAddressRequest.newBuilder()
                .addAdvertiserId(ADVERTISER_ID)
                .build();
    }

    private GetBillingAddressResponse getDefaultBillingAddressResponse(final BillingAddress billingAddress) {
        return GetBillingAddressResponse.newBuilder()
                .addBillingAddress(billingAddress)
                .build();
    }
}
