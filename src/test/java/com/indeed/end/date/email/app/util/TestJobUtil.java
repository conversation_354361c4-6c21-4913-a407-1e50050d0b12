package com.indeed.end.date.email.app.util;

import com.indeed.cally.api.proto.GetCandidateCountsByJobIDRequest;
import com.indeed.cally.api.proto.GetCandidateCountsByJobIDResponse;
import com.indeed.cally.api.proto.JobIDtoCounts;
import com.indeed.cally.client.CallyService;
import com.indeed.dradis.common.proto.JobProtos.DailyTrafficStats;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.monetization.platform.library.core.adtrafficstats.GetDailyAdTrafficStats;
import com.indeed.util.core.time.WallClock;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestJobUtil {

    private static final int ADVERTISER_ID = 123;
    private static final int ATS_JOB_ID = 1;

    @Mock
    private CallyService callyService;

    @Mock
    private WallClock wallClock;

    @Mock
    private MetricsEmitter metricsEmitter;

    @Mock
    private GetDailyAdTrafficStats getDailyAdTrafficStats;

    @InjectMocks
    private JobUtil jobUtil;

    // ------ getCandidateCountsForJobUnsafe ------
    @Test
    public void testGetCandidateCountsForJobUnsafe() throws IOException {
        final Integer candidateCount = 5;
        final GetCandidateCountsByJobIDRequest request = GetCandidateCountsByJobIDRequest.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .addAtsJobId(ATS_JOB_ID)
                .build();

        final GetCandidateCountsByJobIDResponse response = GetCandidateCountsByJobIDResponse.newBuilder()
                .addJobIDtoCounts(JobIDtoCounts.newBuilder()
                        .setAtsJobId(ATS_JOB_ID)
                        .setCandidateCount(candidateCount)
                        .build())
                .build();

        when(callyService.getCandidateCountsByJobID(request)).thenReturn(response);

        final Optional<Integer> result = jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID);
        assertTrue(result.isPresent());
        assertEquals(candidateCount, result.get());
    }

    @Test
    public void testGetCandidateCountsForJobUnsafe_invalidAdvertiserId() throws IOException {
        final Optional<Integer> result = jobUtil.getCandidateCountsForJobUnsafe(0, ATS_JOB_ID);
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetCandidateCountsForJobUnsafe_invalidAtsJobId() throws IOException {
        final Optional<Integer> result = jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, 0);
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetCandidateCountsForJobUnsafe_zeroCandidates() throws IOException {
        final GetCandidateCountsByJobIDRequest request = GetCandidateCountsByJobIDRequest.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .addAtsJobId(ATS_JOB_ID)
                .build();

        when(callyService.getCandidateCountsByJobID(request))
                .thenReturn(GetCandidateCountsByJobIDResponse.getDefaultInstance());

        final Optional<Integer> result = jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID);
        assertTrue(result.isPresent());
        assertEquals(Integer.valueOf(0), result.get());
    }

    @Test
    public void testGetCandidateCountsForJobUnsafe_responseMissingCandidateCount() throws IOException {
        final GetCandidateCountsByJobIDRequest request = GetCandidateCountsByJobIDRequest.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .addAtsJobId(ATS_JOB_ID)
                .build();

        final GetCandidateCountsByJobIDResponse response = GetCandidateCountsByJobIDResponse.newBuilder()
                .addJobIDtoCounts(
                        JobIDtoCounts.newBuilder().setAtsJobId(ATS_JOB_ID).build())
                .build();

        when(callyService.getCandidateCountsByJobID(request)).thenReturn(response);

        final Optional<Integer> result = jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID);
        assertFalse(result.isPresent());
    }

    // ------ getTrafficStatsForJobUnsafe ------
    @Test
    public void GIVEN_invalidAdvertiserId_WHEN_getTrafficStatsForJobUnsafe_THEN_returnEmpty() throws Exception {
        // WHEN
        final Optional<DailyTrafficStats> result = jobUtil.getTrafficStatsForJobUnsafe(0, ATS_JOB_ID);

        //  THEN
        assertFalse(result.isPresent());
    }

    @Test
    public void GIVEN_invalidAtsJobId_WHEN_getTrafficStatsForJobUnsafe_THEN_returnEmpty() throws Exception {
        // WHEN
        final Optional<DailyTrafficStats> result = jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, 0);

        // THEN
        assertFalse(result.isPresent());
    }

    @Test
    public void GIVEN_totalsNull_and_statsEmpty_WHEN_getTrafficStatsForJobUnsafe_THEN_returnEmpty() throws Exception {
        // GIVEN
        final GetDailyAdTrafficStats.Result dailyAdTrafficStatsResult =
                new GetDailyAdTrafficStats.Result(Collections.emptyList(), null);

        when(wallClock.currentTimeMillis()).thenReturn(1000L, 2000L);
        when(getDailyAdTrafficStats.getDailyTrafficStats(any(GetDailyAdTrafficStats.Parameters.class)))
                .thenReturn(dailyAdTrafficStatsResult);

        // WHEN
        final Optional<DailyTrafficStats> result = jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertFalse(result.isPresent());
    }

    @Test
    public void GIVEN_totalsNull_and_statsNonEmpty_WHEN_getTrafficStatsForJobUnsafe_THEN_returnFirstStat()
            throws Exception {
        // GIVEN
        when(wallClock.currentTimeMillis()).thenReturn(1000L, 2000L);

        final DailyTrafficStats first = DailyTrafficStats.newBuilder().build();
        final List<DailyTrafficStats> statsList = new ArrayList<>();
        statsList.add(first);

        final GetDailyAdTrafficStats.Result dailyAdTrafficStatsResult =
                new GetDailyAdTrafficStats.Result(statsList, null);

        when(getDailyAdTrafficStats.getDailyTrafficStats(any(GetDailyAdTrafficStats.Parameters.class)))
                .thenReturn(dailyAdTrafficStatsResult);

        // WHEN
        final Optional<DailyTrafficStats> stats = jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertTrue(stats.isPresent());
        assertEquals(first, stats.get());
    }

    @Test
    public void GIVEN_totalsNonEmpty_WHEN_getTrafficStatsForJobUnsafe_THEN_returnFirstTotal() throws Exception {
        // GIVEN
        when(wallClock.currentTimeMillis()).thenReturn(1000L, 2000L);

        final DailyTrafficStats total = DailyTrafficStats.newBuilder().build();
        final List<DailyTrafficStats> totalsList = new ArrayList<>();
        totalsList.add(total);

        final GetDailyAdTrafficStats.Result dailyAdTrafficStatsResult =
                new GetDailyAdTrafficStats.Result(Collections.emptyList(), totalsList);

        when(getDailyAdTrafficStats.getDailyTrafficStats(any(GetDailyAdTrafficStats.Parameters.class)))
                .thenReturn(dailyAdTrafficStatsResult);

        // WHEN
        final Optional<DailyTrafficStats> stats = jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertTrue(stats.isPresent());
        assertEquals(total, stats.get());
    }

    @Test
    public void GIVEN_totalsEmpty_and_statsNonEmpty_WHEN_getTrafficStatsForJobUnsafe_THEN_returnFirstStat()
            throws Exception {
        // GIVEN
        when(wallClock.currentTimeMillis()).thenReturn(1000L, 2000L);

        final DailyTrafficStats first = DailyTrafficStats.newBuilder().build();
        final java.util.List<DailyTrafficStats> statsList = new java.util.ArrayList<>();
        statsList.add(first);

        final java.util.List<DailyTrafficStats> emptyTotals = java.util.Collections.emptyList();
        final GetDailyAdTrafficStats.Result dailyAdTrafficStatsResult =
                new GetDailyAdTrafficStats.Result(statsList, emptyTotals);

        when(getDailyAdTrafficStats.getDailyTrafficStats(any(GetDailyAdTrafficStats.Parameters.class)))
                .thenReturn(dailyAdTrafficStatsResult);

        // WHEN
        final Optional<DailyTrafficStats> stats = jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID);

        // THEN
        assertTrue(stats.isPresent());
        assertEquals(first, stats.get());
    }
}
