package com.indeed.end.date.email.app.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@ExtendWith(MockitoExtension.class)
public class LogEntryUtilTest {

    private final LogEntryUtil util = new LogEntryUtil();

    @Test
    public void GIVEN_params_WHEN_logTypeWithParamMap_THEN_commit() {
        // GIVEN
        final String logType = "END_DATE_EMAIL_LOG";
        final Map<String, String> params = Map.of("key1", "val1", "key2", "val2");

        // WHEN - THEN
        assertDoesNotThrow(() -> util.logTypeWithParamMap(logType, params));
    }

    @Test
    public void GIVEN_emptyParams_WHEN_logTypeWithParamMap_THEN_commit() {
        // GIVEN
        final String logType = "END_DATE_EMAIL_LOG_EMPTY";
        final Map<String, String> params = Map.of();

        // WHEN - THEN
        assertDoesNotThrow(() -> util.logTypeWithParamMap(logType, params));
    }
}
