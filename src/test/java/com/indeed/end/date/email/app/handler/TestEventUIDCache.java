package com.indeed.end.date.email.app.handler;

import net.spy.memcached.MemcachedClient;
import net.spy.memcached.internal.OperationFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.indeed.end.date.email.app.handler.EventUIDCache.AddCacheStatus.ADDED_TO_CACHE;
import static com.indeed.end.date.email.app.handler.EventUIDCache.AddCacheStatus.ALREADY_IN_CACHE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestEventUIDCache {

    private static final String UID = "uid";

    @InjectMocks
    EventUIDCache eventUIDCache;

    @Mock
    MemcachedClient mockMemcachedClient;

    @Mock
    OperationFuture<Boolean> mockFuture;

    // ------ safeAddUIDToProcessingCache ------
    @Test
    public void GIVEN_uid_WHEN_safeAddUIDToProcessingCache_THEN_returnsADDED_TO_CACHE() throws Exception {
        // GIVEN
        when(mockFuture.get()).thenReturn(true);
        when(mockMemcachedClient.add(anyString(), anyInt(), anyString())).thenReturn(mockFuture);

        // WHEN
        final EventUIDCache.AddCacheStatus result = eventUIDCache.safeAddUIDToProcessingCache(UID);

        // THEN
        assertEquals(ADDED_TO_CACHE, result);
    }

    @Test
    public void GIVEN_uid_WHEN_safeAddUIDToProcessingCache_THEN_returnsALREADY_IN_CACHE() throws Exception {
        // GIVEN
        when(mockFuture.get()).thenReturn(false);
        when(mockMemcachedClient.add(anyString(), anyInt(), anyString())).thenReturn(mockFuture);

        // WHEN
        final EventUIDCache.AddCacheStatus result = eventUIDCache.safeAddUIDToProcessingCache(UID);

        // THEN
        assertEquals(ALREADY_IN_CACHE, result);
    }

    @Test
    public void GIVEN_exceptionFromAdd_WHEN_safeAddUIDToProcessingCache_THEN_NotThrows() {
        // GIVEN
        when(mockMemcachedClient.add(anyString(), anyInt(), anyString())).thenThrow(new RuntimeException());

        // WHEN
        assertDoesNotThrow(() -> eventUIDCache.safeAddUIDToProcessingCache(UID));

        // THEN
    }

    // ------ safeAddUIDToProcessedCache ------

    @Test
    public void GIVEN_uid_WHEN_safeAddUIDToProcessedCache_THEN_returnsADDED_TO_CACHE() throws Exception {
        // GIVEN
        when(mockFuture.get()).thenReturn(true);
        when(mockMemcachedClient.add(anyString(), anyInt(), anyString())).thenReturn(mockFuture);

        // WHEN
        final EventUIDCache.AddCacheStatus result = eventUIDCache.safeAddUIDToProcessedCache(UID);

        // THEN
        assertEquals(ADDED_TO_CACHE, result);
    }

    @Test
    public void GIVEN_uid_WHEN_safeAddUIDToProcessedCache_THEN_returnsALREADY_IN_CACHE() throws Exception {
        // GIVEN
        when(mockFuture.get()).thenReturn(false);
        when(mockMemcachedClient.add(anyString(), anyInt(), anyString())).thenReturn(mockFuture);

        // WHEN
        final EventUIDCache.AddCacheStatus result = eventUIDCache.safeAddUIDToProcessedCache(UID);

        // THEN
        assertEquals(ALREADY_IN_CACHE, result);
    }

    @Test
    public void GIVEN_exceptionFromAdd_WHEN_safeAddUIDToProcessedCache_THEN_NotThrows() {
        // GIVEN
        when(mockMemcachedClient.add(anyString(), anyInt(), anyString())).thenThrow(new RuntimeException());

        // WHEN
        assertDoesNotThrow(() -> eventUIDCache.safeAddUIDToProcessedCache(UID));

        // THEN
    }

    // ------ isUIDInProcessedCache ------
    @Test
    public void GIVEN_uidInCache_WHEN_isUIDInProcessedCache_THEN_returnsIN_CACHE() {
        // GIVEN
        when(mockMemcachedClient.get(anyString())).thenReturn(new Object());

        // WHEN
        final EventUIDCache.GetCacheStatus result = eventUIDCache.isUIDInProcessedCache(UID);

        // THEN
        assertEquals(EventUIDCache.GetCacheStatus.IN_CACHE, result);
    }

    @Test
    public void GIVEN_uidNotInCache_WHEN_isUIDInProcessedCache_THEN_returnsNOT_IN_CACHE() {
        // GIVEN
        when(mockMemcachedClient.get(anyString())).thenReturn(null);

        // WHEN
        final EventUIDCache.GetCacheStatus result = eventUIDCache.isUIDInProcessedCache(UID);

        // THEN
        assertEquals(EventUIDCache.GetCacheStatus.NOT_IN_CACHE, result);
    }

    @Test
    public void GIVEN_exceptionFromGet_WHEN_isUIDInProcessedCache_THEN_NotThrows() {
        // GIVEN
        when(mockMemcachedClient.get(anyString())).thenThrow(new RuntimeException());

        // WHEN

        assertDoesNotThrow(() -> eventUIDCache.isUIDInProcessedCache(UID));

        // THEN

    }

    // ------ safeRemoveFromProcessingCache ------
    @Test
    public void GIVEN_uid_WHEN_safeRemoveFromProcessingCache_THEN_callsDelete() {
        // GIVEN

        // WHEN
        eventUIDCache.safeRemoveFromProcessingCache(UID);

        // THEN
        verify(mockMemcachedClient, times(1)).delete("end-date-email-adcentral-events-daemon:processing:uid");
    }

    @Test
    public void GIVEN_exceptionFromDelete_WHEN_safeRemoveFromProcessingCache_THEN_NotThrows() {
        // GIVEN
        when(mockMemcachedClient.delete(anyString())).thenThrow(new RuntimeException());

        // WHEN
        assertDoesNotThrow(() -> eventUIDCache.safeRemoveFromProcessingCache(UID));
    }
}
