package com.indeed.end.date.email.app.handler;

import com.indeed.status.core.Urgency;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.internal.OperationFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MemcachedDependencyTest {

    @Mock
    private MemcachedClient memcachedClient;

    @Mock
    private OperationFuture<Boolean> future;

    @Test
    public void GIVEN_addSucceeds_WHEN_ping_THEN_waitForFutureAndSucceed() throws Exception {
        // GIVEN
        when(memcachedClient.add(anyString(), anyInt(), anyString())).thenReturn(future);
        when(future.get()).thenReturn(true);
        final MemcachedDependency dep = new MemcachedDependency(memcachedClient, Urgency.REQUIRED, 5000);

        // WHEN
        assertDoesNotThrow(dep::ping);

        // THEN
        verify(memcachedClient, times(1)).add(anyString(), anyInt(), anyString());
        verify(future, times(1)).get();
    }

    @Test
    public void GIVEN_futureThrows_WHEN_ping_THEN_throw() throws Exception {
        // GIVEN
        when(memcachedClient.add(anyString(), anyInt(), anyString())).thenReturn(future);
        when(future.get()).thenThrow(new RuntimeException());
        final MemcachedDependency dep = new MemcachedDependency(memcachedClient, Urgency.REQUIRED, 5000);

        // WHEN
        assertThrows(Exception.class, dep::ping);

        // THEN
        verify(memcachedClient, times(1)).add(anyString(), anyInt(), anyString());
        verify(future, times(1)).get();
    }
}
