package com.indeed.end.date.email.app.adceventsdaemon.handler;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.indeed.adcentral.proto.events.EventProducerProto;
import com.indeed.adcentral.proto.events.EventProducerProto.EventProducerMessage.ActivityType;
import com.indeed.advertiserservice.client.AdvertiserServiceRpcClientException;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.Advertiser;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.AdvertiserProperty;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.PropertyName;
import com.indeed.common.base.IndeedStagingLevel;
import com.indeed.common.message.ConsumerResponse;
import com.indeed.common.money.currency.LocalBillableCurrencyAmountFactory;
import com.indeed.common.money.currency.LocalCurrencyAmount;
import com.indeed.common.money.util.LocalCurrencyAmountFormat;
import com.indeed.dradis.common.encryption.EncryptionUtil;
import com.indeed.dradis.common.proto.JobProtos.DailyTrafficStats;
import com.indeed.dradis.common.util.DradisJobUtils;
import com.indeed.dradis.common.util.SuperSmartDateFormatter;
import com.indeed.end.date.email.app.enums.ErrorCause;
import com.indeed.end.date.email.app.enums.ServiceName;
import com.indeed.end.date.email.app.groups.EndDateEmailAppProctorGroups;
import com.indeed.end.date.email.app.logging.LogEntryKeys;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.end.date.email.app.model.EndDateEmailJobData;
import com.indeed.end.date.email.app.model.RavenEmail;
import com.indeed.end.date.email.app.onegraph.util.HostedSponsoredJobsService;
import com.indeed.end.date.email.app.util.AdvertiserUtil;
import com.indeed.end.date.email.app.util.EmailUtil;
import com.indeed.end.date.email.app.util.JobUtil;
import com.indeed.end.date.email.app.util.LogEntryUtil;
import com.indeed.end.date.email.app.util.ProctorUtil;
import com.indeed.raven.delivery.client.RavenDeliveryRpcClientException;
import com.indeed.raven.delivery.rpc.Status;
import com.indeed.util.core.time.WallClock;
import org.joda.money.CurrencyUnit;
import org.jooq.tools.json.JSONObject;
import org.jooq.tools.json.JSONValue;
import org.jooq.tools.json.ParseException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import static com.indeed.end.date.email.app.adceventsdaemon.handler.EndDateEmailHandler.RAVEN_CAMPAIGN_NAME_EN_US;
import static com.indeed.end.date.email.app.adceventsdaemon.handler.EndDateEmailHandler.RAVEN_CAMPAIGN_NAME_INTL;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestEndDateEmailHandler {

    private static final int ADVERTISER_ID = 1;
    private static final int ACCOUNT_ID = 654321;
    private static final int ATS_JOB_ID = 123;
    private static final String KEY = "keyOne";
    private static final String VALUE = "valueOne";
    private static final String ENCRYPTED_JOB_ID = "encryptedTestId";
    private static final String CAMPAIGN_NAME = "campaignName";
    private static final String SESSION_UID = "sessionUidStr";
    private static final String FRENCH_LOCALE = "fr";
    private static final Optional<String> CANADA_COUNTRY = Optional.of("CA");
    private static final String COMPANY = "SomeCompany";
    private static final String JOB_TITLE = "testTitle";
    private static final String LOCATION = "Seattle, WA";
    private static final String EN_LANGUAGE = "en";
    private static final String US_COUNTRY = "US";
    private static final Optional<String> MAYBE_US_COUNTRY = Optional.of(US_COUNTRY);
    private static final String RAVEN_DEDUPE = String.format("%d_%d_%s", ADVERTISER_ID, ATS_JOB_ID, "********");
    private static final String JOB_HASH = String.format("%d_%d", ADVERTISER_ID, ATS_JOB_ID);
    private static final Long COST_MINOR_UNITS = 10000L;
    private static final long MILLIS = 1553900194152L;

    private static final long ZERO_DAYS_EARLY_ZERO = 0L;

    private static final long THREE_DAYS_EARLY = 3L;
    private static final EventProducerProto.EventProducerMessage PAUSE_EVENT =
            EventProducerProto.EventProducerMessage.newBuilder()
                    .setAdvertiserId(ADVERTISER_ID)
                    .setActivityType(ActivityType.PAUSE_JOB)
                    .setJsonString(
                            "{\"ats_job_id\": 123, \"updateStatusReason\": \"SPONSORED_JOB_END_DATE_REACHED\", \"suppressExternal\": true, \"days_early\":0}")
                    .build();

    private static final EventProducerProto.EventProducerMessage SEND_END_DATE_REMINDER_EVENT =
            EventProducerProto.EventProducerMessage.newBuilder()
                    .setAdvertiserId(ADVERTISER_ID)
                    .setActivityType(ActivityType.SEND_END_DATE_REMINDER)
                    .setJsonString("{\"ats_job_id\": 123, \"suppressExternal\": true, \"days_early\":3}")
                    .build();
    private static final EventProducerProto.EventProducerMessage SEND_END_DATE_REMINDER_EVENT_NO_DAYS_EARLY =
            EventProducerProto.EventProducerMessage.newBuilder()
                    .setAdvertiserId(ADVERTISER_ID)
                    .setActivityType(ActivityType.SEND_END_DATE_REMINDER)
                    .setJsonString("{\"ats_job_id\": 123, \"suppressExternal\": true}")
                    .build();

    private static final EventProducerProto.EventProducerMessage INVALID_MESSAGE =
            EventProducerProto.EventProducerMessage.newBuilder()
                    .setAdvertiserId(ADVERTISER_ID)
                    .setActivityType(ActivityType.SEND_END_DATE_REMINDER)
                    .setJsonString("")
                    .build();

    @Mock
    private LogEntryUtil logEntryUtil;

    @Mock
    private AdvertiserUtil advertiserUtil;

    @Mock
    private EmailUtil emailUtil;

    @Mock
    private JobUtil jobUtil;

    @Mock
    private WallClock wallClock;

    private EndDateEmailHandler handler;

    @Mock
    private MetricsEmitter metricsEmitter;

    @Mock
    private HostedSponsoredJobsService hostedSponsoredJobsService;

    @Mock
    private ProctorUtil proctorUtil;

    @BeforeEach
    public void setUp() {
        EndDateEmailAppProctorGroups proctorGroups = mock(EndDateEmailAppProctorGroups.class);
        lenient()
                .when(proctorUtil.getProctorGroups(anyInt(), anyString(), anyString()))
                .thenReturn(proctorGroups);

        handler = new EndDateEmailHandler(
                logEntryUtil,
                advertiserUtil,
                emailUtil,
                jobUtil,
                wallClock,
                metricsEmitter,
                hostedSponsoredJobsService,
                proctorUtil);
    }

    private static String getExpectedFormattedSpend(
            final Locale locale, final String currency, final long totalCostLM) {
        final LocalCurrencyAmountFormat formatter = new LocalCurrencyAmountFormat(locale);
        final CurrencyUnit currencyUnit = LocalBillableCurrencyAmountFactory.getSupportedCurrency(currency);
        final LocalCurrencyAmount amount = LocalBillableCurrencyAmountFactory.fromMinorUnits(currencyUnit, totalCostLM);
        return formatter.format(amount);
    }

    @Test
    public void testHandleEvent_sent() throws Exception {

        // Formatting is changing in Java 17 for some reason, so calculating formatting on the fly instead of
        // hard-coding to CA$0,00
        final String expectedFrenchFormat =
                getExpectedFormattedSpend(Locale.CANADA_FRENCH, CurrencyUnit.CAD.getCode(), 0L);

        testSent(
                EN_LANGUAGE,
                RAVEN_CAMPAIGN_NAME_EN_US,
                MAYBE_US_COUNTRY,
                Locale.US,
                AdvertiserServiceProtos.CurrencyCode.USD,
                "$0.00",
                ZERO_DAYS_EARLY_ZERO,
                PAUSE_EVENT);
        testSent(
                FRENCH_LOCALE,
                RAVEN_CAMPAIGN_NAME_INTL,
                CANADA_COUNTRY,
                Locale.CANADA_FRENCH,
                AdvertiserServiceProtos.CurrencyCode.CAD,
                expectedFrenchFormat,
                ZERO_DAYS_EARLY_ZERO,
                PAUSE_EVENT);

        verify(metricsEmitter, times(2)).emitEndDateEmailSent();
    }

    @Test
    public void testHandleEvent_emailWithoutBillingCountry() throws Exception {
        testSent(
                EN_LANGUAGE,
                RAVEN_CAMPAIGN_NAME_EN_US,
                Optional.empty(),
                Locale.US,
                AdvertiserServiceProtos.CurrencyCode.USD,
                "$0.00",
                ZERO_DAYS_EARLY_ZERO,
                PAUSE_EVENT);
    }

    @Test
    public void testHandleEvent_sentReminderEmail() throws Exception {
        testSent(
                EN_LANGUAGE,
                RAVEN_CAMPAIGN_NAME_EN_US,
                MAYBE_US_COUNTRY,
                Locale.US,
                AdvertiserServiceProtos.CurrencyCode.USD,
                "$0.00",
                THREE_DAYS_EARLY,
                SEND_END_DATE_REMINDER_EVENT);
    }

    @Test
    public void testHandleEvent_sentReminderEmailNoDaysEarly() throws Exception {
        testSent(
                EN_LANGUAGE,
                RAVEN_CAMPAIGN_NAME_EN_US,
                MAYBE_US_COUNTRY,
                Locale.US,
                AdvertiserServiceProtos.CurrencyCode.USD,
                "$0.00",
                ZERO_DAYS_EARLY_ZERO,
                SEND_END_DATE_REMINDER_EVENT_NO_DAYS_EARLY);
    }

    private void testSent(
            final String language,
            final String ravenCampaign,
            final Optional<String> country,
            final Locale locale,
            final AdvertiserServiceProtos.CurrencyCode currency,
            final String totalCost,
            final long daysEarly,
            final EventProducerProto.EventProducerMessage eventProducerMessage)
            throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(currency)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(language)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        when(advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID)).thenReturn(country);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID))
                .thenReturn(Optional.of(DailyTrafficStats.getDefaultInstance()));
        when(wallClock.currentTimeMillis()).thenReturn(MILLIS);

        final Map<String, Object> expectedDataMap = ImmutableMap.<String, Object>builder()
                .put(EndDateEmailHandler.JOB_TITLE_KEY, JOB_TITLE)
                .put(EndDateEmailHandler.JOB_LOCATION_KEY, LOCATION)
                .put(EndDateEmailHandler.COMPANY_KEY, COMPANY)
                .put(EndDateEmailHandler.CANDIDATE_COUNT_KEY, 5)
                .put(EndDateEmailHandler.NON_SPONSORED_JOB_APPS_KEY, 3)
                .put(EndDateEmailHandler.END_DATE_KEY, "March 27")
                .put(EndDateEmailHandler.DISPLAY_TOTAL_COST_KEY, totalCost)
                .put(EndDateEmailHandler.RAVEN_DAYS_EARLY_KEY, daysEarly)
                .put(EndDateEmailHandler.ENCRYPTED_JOB_ID_KEY, ENCRYPTED_JOB_ID)
                .put(EndDateEmailHandler.RAVEN_DEDUPLICATION_ID, RAVEN_DEDUPE)
                .put(EndDateEmailHandler.JOB_HASH, JOB_HASH)
                .build();
        final RavenEmail ravenEmail = RavenEmail.builder()
                .ravenSessionUid(SESSION_UID)
                .status(Status.SENT)
                .advertiserId(ADVERTISER_ID)
                .dataMap(expectedDataMap)
                .campaignName(ravenCampaign)
                .build();
        when(emailUtil.sendEmailUnsafe(ADVERTISER_ID, ravenCampaign, expectedDataMap, locale))
                .thenReturn(Optional.of(ravenEmail));

        final ImmutableMap.Builder<String, String> expectedLogEntryParams = ImmutableMap.<String, String>builder()
                .put(LogEntryKeys.ADVERTISER_ID_KEY, String.valueOf(ADVERTISER_ID))
                .put(LogEntryKeys.ATS_JOB_ID_KEY, String.valueOf(ATS_JOB_ID))
                .put(LogEntryKeys.ACTIVITY_TYPE_KEY, ActivityType.PAUSE_JOB.toString())
                .put(LogEntryKeys.RAVEN_TRACKING_KEY, SESSION_UID)
                .put(LogEntryKeys.RAVEN_CAMPAIGN_NAME, ravenCampaign)
                .put(LogEntryKeys.RAVEN_DELIVERY_STATUS_KEY, Status.SENT.toString())
                .put(LogEntryKeys.SEND_RAVEN_EMAIL, "1");
        expectedDataMap.forEach((k, v) -> expectedLogEntryParams.put(k, String.valueOf(v)));

        logEntryUtil.logTypeWithParamMap(LogEntryKeys.END_DATE_EMAIL_LOG_ENTRY, expectedLogEntryParams.build());

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class);
                final MockedStatic<DradisJobUtils> mockedDradisJobUtils = mockStatic(DradisJobUtils.class);
                final MockedStatic<SuperSmartDateFormatter> mockedSuperSmartDateFormatter =
                        mockStatic(SuperSmartDateFormatter.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn(ENCRYPTED_JOB_ID);
            mockedDradisJobUtils
                    .when(() -> DradisJobUtils.getDisplayLocationStringFromStrings(
                            ImmutableList.of(LOCATION), locale.getCountry(), locale, false))
                    .thenReturn(LOCATION);

            mockedSuperSmartDateFormatter
                    .when(() -> SuperSmartDateFormatter.formatDate(anyLong(), anyLong(), any(), any()))
                    .thenReturn("March 27");

            final ConsumerResponse pauseResponse = handler.handleEvent(eventProducerMessage);
            assertEquals(ConsumerResponse.ACK, pauseResponse);
        }
    }

    @Test
    public void testHandleEvent_filtered() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        when(advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID)).thenReturn(CANADA_COUNTRY);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID))
                .thenReturn(Optional.of(DailyTrafficStats.getDefaultInstance()));
        when(wallClock.currentTimeMillis()).thenReturn(MILLIS);

        // Formatting is changing in Java 17 for some reason, so calculating formatting on the fly instead of
        // hard-coding to CA$0,00
        final String expectedFrenchFormat =
                getExpectedFormattedSpend(Locale.CANADA_FRENCH, CurrencyUnit.CAD.getCode(), 0L);

        final Map<String, Object> expectedDataMap = ImmutableMap.<String, Object>builder()
                .put(EndDateEmailHandler.JOB_TITLE_KEY, JOB_TITLE)
                .put(EndDateEmailHandler.JOB_LOCATION_KEY, LOCATION)
                .put(EndDateEmailHandler.COMPANY_KEY, COMPANY)
                .put(EndDateEmailHandler.CANDIDATE_COUNT_KEY, 5)
                .put(EndDateEmailHandler.NON_SPONSORED_JOB_APPS_KEY, 3)
                .put(EndDateEmailHandler.END_DATE_KEY, "March 27")
                .put(EndDateEmailHandler.DISPLAY_TOTAL_COST_KEY, expectedFrenchFormat)
                .put(EndDateEmailHandler.RAVEN_DAYS_EARLY_KEY, 0L)
                .put(EndDateEmailHandler.ENCRYPTED_JOB_ID_KEY, ENCRYPTED_JOB_ID)
                .put(EndDateEmailHandler.RAVEN_DEDUPLICATION_ID, RAVEN_DEDUPE)
                .put(EndDateEmailHandler.JOB_HASH, JOB_HASH)
                .build();
        final RavenEmail ravenEmail = RavenEmail.builder()
                .ravenSessionUid(SESSION_UID)
                .status(Status.FILTER)
                .advertiserId(ADVERTISER_ID)
                .dataMap(expectedDataMap)
                .campaignName(RAVEN_CAMPAIGN_NAME_INTL)
                .build();
        when(emailUtil.sendEmailUnsafe(ADVERTISER_ID, RAVEN_CAMPAIGN_NAME_INTL, expectedDataMap, Locale.CANADA_FRENCH))
                .thenReturn(Optional.of(ravenEmail));

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class);
                final MockedStatic<DradisJobUtils> mockedDradisJobUtils = mockStatic(DradisJobUtils.class);
                final MockedStatic<SuperSmartDateFormatter> mockedSuperSmartDateFormatter =
                        mockStatic(SuperSmartDateFormatter.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn(ENCRYPTED_JOB_ID);
            mockedDradisJobUtils
                    .when(() -> DradisJobUtils.getDisplayLocationStringFromStrings(
                            ImmutableList.of(LOCATION), Locale.CANADA_FRENCH.getCountry(), Locale.CANADA_FRENCH, false))
                    .thenReturn(LOCATION);

            mockedSuperSmartDateFormatter
                    .when(() -> SuperSmartDateFormatter.formatDate(anyLong(), anyLong(), any(), any()))
                    .thenReturn("March 27");

            final ConsumerResponse pauseResponse = handler.handleEvent(PAUSE_EVENT);
            assertEquals(ConsumerResponse.ACK, pauseResponse);
        }

        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.RAVEN_FILTERED);
    }

    @Test
    public void testHandleEvent_noStatus() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        when(advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID)).thenReturn(CANADA_COUNTRY);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID))
                .thenReturn(Optional.of(DailyTrafficStats.getDefaultInstance()));
        when(wallClock.currentTimeMillis()).thenReturn(MILLIS);

        // Formatting is changing in Java 17 for some reason, so calculating formatting on the fly instead of
        // hard-coding to CA$0,00
        final String expectedFrenchFormat =
                getExpectedFormattedSpend(Locale.CANADA_FRENCH, CurrencyUnit.CAD.getCode(), 0L);

        final Map<String, Object> expectedDataMap = ImmutableMap.<String, Object>builder()
                .put(EndDateEmailHandler.JOB_TITLE_KEY, JOB_TITLE)
                .put(EndDateEmailHandler.JOB_LOCATION_KEY, LOCATION)
                .put(EndDateEmailHandler.COMPANY_KEY, COMPANY)
                .put(EndDateEmailHandler.CANDIDATE_COUNT_KEY, 5)
                .put(EndDateEmailHandler.NON_SPONSORED_JOB_APPS_KEY, 3)
                .put(EndDateEmailHandler.END_DATE_KEY, "March 27")
                .put(EndDateEmailHandler.DISPLAY_TOTAL_COST_KEY, expectedFrenchFormat)
                .put(EndDateEmailHandler.RAVEN_DAYS_EARLY_KEY, 0L)
                .put(EndDateEmailHandler.ENCRYPTED_JOB_ID_KEY, ENCRYPTED_JOB_ID)
                .put(EndDateEmailHandler.RAVEN_DEDUPLICATION_ID, RAVEN_DEDUPE)
                .put(EndDateEmailHandler.JOB_HASH, JOB_HASH)
                .build();
        final RavenEmail ravenEmail = RavenEmail.builder()
                .ravenSessionUid(SESSION_UID)
                .advertiserId(ADVERTISER_ID)
                .dataMap(expectedDataMap)
                .campaignName(RAVEN_CAMPAIGN_NAME_INTL)
                .build();
        when(emailUtil.sendEmailUnsafe(ADVERTISER_ID, RAVEN_CAMPAIGN_NAME_INTL, expectedDataMap, Locale.CANADA_FRENCH))
                .thenReturn(Optional.of(ravenEmail));

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class);
                final MockedStatic<DradisJobUtils> mockedDradisJobUtils = mockStatic(DradisJobUtils.class);
                final MockedStatic<SuperSmartDateFormatter> mockedSuperSmartDateFormatter =
                        mockStatic(SuperSmartDateFormatter.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn(ENCRYPTED_JOB_ID);
            mockedDradisJobUtils
                    .when(() -> DradisJobUtils.getDisplayLocationStringFromStrings(
                            ImmutableList.of(LOCATION), Locale.CANADA_FRENCH.getCountry(), Locale.CANADA_FRENCH, false))
                    .thenReturn(LOCATION);

            mockedSuperSmartDateFormatter
                    .when(() -> SuperSmartDateFormatter.formatDate(anyLong(), anyLong(), any(), any()))
                    .thenReturn("March 27");

            final ConsumerResponse pauseResponse = handler.handleEvent(PAUSE_EVENT);
            assertEquals(ConsumerResponse.ACK, pauseResponse);
        }
    }

    @Test
    public void testHandleEvent_invalidMessage() throws Exception {
        final ConsumerResponse pauseResponse = handler.handleEvent(INVALID_MESSAGE);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.INVALID_MESSAGE);
        assertEquals(ConsumerResponse.ACK, pauseResponse);
    }

    @Test
    public void testHandleEvent_invalidAdvertiserId() {

        final ConsumerResponse response = handler.handleEvent(EventProducerProto.EventProducerMessage.newBuilder()
                .setAdvertiserId(0)
                .setActivityType(ActivityType.PAUSE_JOB)
                .setJsonString("{\"ats_job_id\": 123, \"send-email\": 1}")
                .build());
        assertEquals(ConsumerResponse.ACK, response);

        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.INVALID_MESSAGE);
    }

    @Test
    public void testHandleEvent_jsonParseException() {

        final ConsumerResponse response = handler.handleEvent(EventProducerProto.EventProducerMessage.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setActivityType(ActivityType.PAUSE_JOB)
                .setJsonString("can't be parsed!!!!")
                .build());
        assertEquals(ConsumerResponse.ACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.INVALID_MESSAGE);
    }

    @Test
    public void testHandleEvent_jsonInvalid() {

        final ConsumerResponse response = handler.handleEvent(EventProducerProto.EventProducerMessage.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setActivityType(ActivityType.PAUSE_JOB)
                .setJsonString("{}")
                .build());
        assertEquals(ConsumerResponse.ACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.INVALID_MESSAGE);
    }

    @Test
    public void testHandleEvent_getAdvertiserServiceException() throws Exception {
        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID))
                .thenThrow(new AdvertiserServiceRpcClientException("failed"));

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
        verify(metricsEmitter).emitEndDateEmailRequeued(ServiceName.ADVERTISER_SERVICE);
    }

    @Test
    public void testHandleEvent_advertiserNotFound() throws Exception {
        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.empty());

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.ADVERTISER_ERROR);
    }

    @Test
    public void testHandleEvent_getAdvertiserPropertiesServiceException() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenThrow(new AdvertiserServiceRpcClientException());

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
        verify(metricsEmitter).emitEndDateEmailRequeued(ServiceName.ADVERTISER_SERVICE);
    }

    @Test
    public void testHandleEvent_advertiserRejected() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue(EndDateEmailHandler.REJECTED)
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.ACK, response);

        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.ADVERTISER_REJECTED);
    }

    @Test
    public void testHandleEvent_jobUtilException() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenThrow(new Exception("failed"));

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
        verify(metricsEmitter).emitEndDateEmailRequeued(ServiceName.ONE_GRAPH);
    }

    @Test
    public void testHandleEvent_jobNotResolved() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.MISSING_JOB);
    }

    @Test
    public void testHandleEvent_jobMissingTitle() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.INVALID_JOB);
    }

    @Test
    public void testHandleEvent_jobMissingLocation() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.INVALID_JOB);
    }

    @Test
    public void testHandleEvent_freeJobFiltered() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(true)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.ACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.FREE_JOB_FILTERED);
    }

    @Test
    public void testHandleEvent_candidateCountException() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenThrow(new IOException());

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
        verify(metricsEmitter).emitEndDateEmailRequeued(ServiceName.CALLY);
    }

    @Test
    public void testHandleEvent_candidateCountMissing() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.empty());

        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
        assertEquals(ConsumerResponse.NACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.CANDIDATES_ERROR);
    }

    @Test
    public void testHandleEvent_encryptionFailure() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn("");

            final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
            assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
        }
    }

    @Test
    public void testHandleEvent_dailyTrafficStatsException() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenThrow(new IOException());

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn(ENCRYPTED_JOB_ID);

            final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
            assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
        }
        verify(metricsEmitter).emitEndDateEmailRequeued(ServiceName.GET_DAILY_AD_TRAFFIC_STATS);
    }

    @Test
    public void testHandleEvent_dailyTrafficStatsExceptionInQA() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenThrow(new IOException());

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn(ENCRYPTED_JOB_ID);

            try (final MockedStatic<IndeedStagingLevel> mockedIndeedStagingLevel =
                    mockStatic(IndeedStagingLevel.class)) {
                mockedIndeedStagingLevel.when(IndeedStagingLevel::get).thenReturn(IndeedStagingLevel.QA);
                final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
                assertEquals(ConsumerResponse.ACK, response);
            }
        }
        verify(metricsEmitter).emitEndDateEmailRequeued(ServiceName.GET_DAILY_AD_TRAFFIC_STATS);
    }

    @Test
    public void testHandleEvent_ravenException() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        when(advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID)).thenReturn(CANADA_COUNTRY);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID))
                .thenReturn(Optional.of(DailyTrafficStats.getDefaultInstance()));
        when(wallClock.currentTimeMillis()).thenReturn(MILLIS);
        when(emailUtil.sendEmailUnsafe(anyInt(), any(), any(), any()))
                .thenThrow(new RavenDeliveryRpcClientException("failed"));

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class);
                final MockedStatic<DradisJobUtils> mockedDradisJobUtils = mockStatic(DradisJobUtils.class);
                final MockedStatic<SuperSmartDateFormatter> mockedSuperSmartDateFormatter =
                        mockStatic(SuperSmartDateFormatter.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn(ENCRYPTED_JOB_ID);
            mockedDradisJobUtils
                    .when(() -> DradisJobUtils.getDisplayLocationStringFromStrings(
                            ImmutableList.of(LOCATION), Locale.CANADA_FRENCH.getCountry(), Locale.CANADA_FRENCH, false))
                    .thenReturn(LOCATION);

            mockedSuperSmartDateFormatter
                    .when(() -> SuperSmartDateFormatter.formatDate(anyLong(), anyLong(), any(), any()))
                    .thenReturn("March 27");

            final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
            assertEquals(ConsumerResponse.NACK_WITH_REQUEUE, response);
        }

        verify(metricsEmitter).emitEndDateEmailRequeued(ServiceName.RAVEN);
    }

    @Test
    public void testHandleEvent_emailNotSentNoException() throws Exception {
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        when(advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID)).thenReturn(CANADA_COUNTRY);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);
        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID))
                .thenReturn(Optional.of(DailyTrafficStats.getDefaultInstance()));
        when(wallClock.currentTimeMillis()).thenReturn(MILLIS);
        when(emailUtil.sendEmailUnsafe(anyInt(), any(), any(), any())).thenReturn(Optional.empty());

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class);
                final MockedStatic<DradisJobUtils> mockedDradisJobUtils = mockStatic(DradisJobUtils.class);
                final MockedStatic<SuperSmartDateFormatter> mockedSuperSmartDateFormatter =
                        mockStatic(SuperSmartDateFormatter.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn(ENCRYPTED_JOB_ID);
            mockedDradisJobUtils
                    .when(() -> DradisJobUtils.getDisplayLocationStringFromStrings(
                            ImmutableList.of(LOCATION), Locale.CANADA_FRENCH.getCountry(), Locale.CANADA_FRENCH, false))
                    .thenReturn(LOCATION);

            mockedSuperSmartDateFormatter
                    .when(() -> SuperSmartDateFormatter.formatDate(anyLong(), anyLong(), any(), any()))
                    .thenReturn("March 27");

            final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);
            assertEquals(ConsumerResponse.NACK, response);
        }

        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.RAVEN_ERROR);
    }

    @Test
    public void testIsValidJson_pauseJobActivity() throws ParseException {
        final String validPauseJobJson =
                "{\"ats_job_id\": 123, \"updateStatusReason\": \"SPONSORED_JOB_END_DATE_REACHED\"}";
        final JSONObject obj = (JSONObject) JSONValue.parseWithException(validPauseJobJson);

        assertTrue(handler.isValidJson(obj, ActivityType.PAUSE_JOB, ADVERTISER_ID));
    }

    @Test
    public void testIsValidJson_invalidUpdateStatusReason() throws ParseException {
        final String validPauseJobJson = "{\"ats_job_id\": 123, \"updateStatusReason\": \"DRAT_PAUSEFTP_AGE\"}";
        final JSONObject obj = (JSONObject) JSONValue.parseWithException(validPauseJobJson);

        assertFalse(handler.isValidJson(obj, ActivityType.PAUSE_JOB, ADVERTISER_ID));
    }

    @Test
    public void testIsValidJson_invalidPauseJobActivity() throws ParseException {
        final String invalidPauseJobJson = "{\"ats_job_id\": 123}";
        final JSONObject obj = (JSONObject) JSONValue.parseWithException(invalidPauseJobJson);

        assertFalse(handler.isValidJson(obj, ActivityType.PAUSE_JOB, ADVERTISER_ID));
    }

    @Test
    public void testIsValidJson_reminderActivity() throws ParseException {
        final String validReminderJson = "{\"ats_job_id\": 123}";
        final JSONObject obj = (JSONObject) JSONValue.parseWithException(validReminderJson);

        assertTrue(handler.isValidJson(obj, ActivityType.SEND_END_DATE_REMINDER, ADVERTISER_ID));
    }

    @Test
    public void testIsValidJson_missingAtsJobId() throws ParseException {
        final String json = "{\"updateStatusReason\": \"SPONSORED_JOB_END_DATE_REACHED\"}";
        final JSONObject obj = (JSONObject) JSONValue.parseWithException(json);

        assertFalse(handler.isValidJson(obj, ActivityType.PAUSE_JOB, ADVERTISER_ID));
    }

    @Test
    public void testIsValidJson_invalidAtsJobId() throws ParseException {
        final String validPauseJobJson =
                "{\"ats_job_id\": 0, \"updateStatusReason\": \"SPONSORED_JOB_END_DATE_REACHED\"}";
        final JSONObject obj = (JSONObject) JSONValue.parseWithException(validPauseJobJson);

        assertFalse(handler.isValidJson(obj, ActivityType.PAUSE_JOB, ADVERTISER_ID));
    }

    @Test
    public void testGetLocaleStringFromProperties() {
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(AdvertiserProperty.newBuilder()
                .setPropertyName(PropertyName.LOCALE)
                .setPropertyValue(FRENCH_LOCALE)
                .build());

        final Optional<String> locale = handler.getLocaleStringFromProperties(advertiserProperties);

        assertTrue(locale.isPresent());
        assertEquals(FRENCH_LOCALE, locale.get());
    }

    @Test
    public void testGetLocaleStringFromProperties_missingProperty() {
        final Optional<String> locale = handler.getLocaleStringFromProperties(Collections.emptyList());

        assertEquals(Optional.empty(), locale);
    }

    @Test
    public void testGetLocaleStringFromProperties_missingPropertyValue() {
        final List<AdvertiserProperty> advertiserProperties =
                ImmutableList.of(AdvertiserProperty.newBuilder().build());

        final Optional<String> locale = handler.getLocaleStringFromProperties(advertiserProperties);

        assertEquals(Optional.empty(), locale);
    }

    @Test
    public void testGetLocaleStringFromProperties_onlyHavePropertyName() {
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(AdvertiserProperty.newBuilder()
                .setPropertyName(PropertyName.LOCALE)
                .build());

        final Optional<String> locale = handler.getLocaleStringFromProperties(advertiserProperties);

        assertEquals(Optional.empty(), locale);
    }

    @Test
    public void testlLocaleForEmail() {
        final Optional<String> languageLocale = Optional.of("EN");
        final Optional<String> billingCountry = Optional.of("US");
        final Locale expectedLocale = new Locale("en", "US");

        final Locale locale = handler.localeForEmail(languageLocale, billingCountry);

        assertEquals(expectedLocale, locale);
    }

    @Test
    public void testlLocaleForEmailNoLanguage() {
        final Optional<String> languageLocale = Optional.empty();
        final Optional<String> billingCountry = Optional.of("US");
        final Locale expectedLocale = new Locale("en", "US");

        final Locale locale = handler.localeForEmail(languageLocale, billingCountry);

        assertEquals(expectedLocale, locale);
    }

    @Test
    public void testlLocaleForEmailLanguageLocaleWithCountry() {
        final Optional<String> languageLocale = Optional.of("fr_CA");
        final Optional<String> billingCountry = Optional.of("FR");
        final Locale expectedLocale = new Locale("fr", "FR");

        final Locale locale = handler.localeForEmail(languageLocale, billingCountry);

        assertEquals(expectedLocale, locale);
    }

    @Test
    public void testlLocaleForEmailNoCountry() {
        final Optional<String> languageLocale = Optional.of("en");
        final Optional<String> billingCountry = Optional.empty();
        final Locale expectedLocale = new Locale("en", "US");

        final Locale locale = handler.localeForEmail(languageLocale, billingCountry);

        assertEquals(expectedLocale, locale);
    }

    @Test
    public void testlLocaleForEmailLanguageLocaleWithDash() {
        final Optional<String> languageLocale = Optional.of("fr-CA");
        final Optional<String> billingCountry = Optional.of("FR");
        final Locale expectedLocale = new Locale("fr", "FR");

        final Locale locale = handler.localeForEmail(languageLocale, billingCountry);

        assertEquals(expectedLocale, locale);
    }

    @Test
    public void testIsRejected() {
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(AdvertiserProperty.newBuilder()
                .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                .setPropertyValue("rejected")
                .build());

        assertTrue(handler.isRejected(ADVERTISER_ID, advertiserProperties));
    }

    @Test
    public void testIsRejected_nonMatchingValue() {
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(AdvertiserProperty.newBuilder()
                .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                .setPropertyValue("notRejected")
                .build());

        assertFalse(handler.isRejected(ADVERTISER_ID, advertiserProperties));
    }

    @Test
    public void testIsRejected_noValue() {
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(AdvertiserProperty.newBuilder()
                .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                .build());

        assertFalse(handler.isRejected(ADVERTISER_ID, advertiserProperties));
    }

    @Test
    public void testIsRejected_missingProperty() {
        assertFalse(handler.isRejected(ADVERTISER_ID, Collections.emptyList()));
    }

    @Test
    public void testGetTotalCostLM() throws Exception {
        final DailyTrafficStats dailyTrafficStats =
                DailyTrafficStats.newBuilder().setCostLocal(COST_MINOR_UNITS).build();
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(dailyTrafficStats));

        final Long totalCostLM = handler.getTotalCostLM(ADVERTISER_ID, ATS_JOB_ID);

        assertEquals(COST_MINOR_UNITS, totalCostLM);
    }

    @Test
    public void testGetTotalCostLMNoDailyTrafficStats() throws Exception {
        final long expectResult = 0L;
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.empty());

        final long totalCostLM = handler.getTotalCostLM(ADVERTISER_ID, ATS_JOB_ID);

        assertEquals(expectResult, totalCostLM);
    }

    @Test
    public void testGetTotalCostLM_exception() throws Exception {
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenThrow(new IOException("failed"));

        final Long totalCostLM = handler.getTotalCostLM(ADVERTISER_ID, ATS_JOB_ID);

        assertNull(totalCostLM);
    }

    @Test
    public void testGetTotalCostLM_missingCostLocal() throws Exception {
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID))
                .thenReturn(Optional.of(DailyTrafficStats.getDefaultInstance()));

        final Long totalCostLM = handler.getTotalCostLM(ADVERTISER_ID, ATS_JOB_ID);

        assertEquals(Long.valueOf(0), totalCostLM);
    }

    @Test
    public void testAddRavenInfoToLoggingParamMap() {
        final ImmutableMap.Builder<String, String> loggingParamsBuilder = ImmutableMap.builder();
        final Map<String, Object> dataMap = ImmutableMap.of(KEY, VALUE);
        final RavenEmail ravenEmail = RavenEmail.builder()
                .advertiserId(ADVERTISER_ID)
                .dataMap(dataMap)
                .campaignName(CAMPAIGN_NAME)
                .status(Status.SENT)
                .ravenSessionUid(SESSION_UID)
                .build();

        handler.addRavenInfoToLoggingParamMap(loggingParamsBuilder, ravenEmail, RAVEN_CAMPAIGN_NAME_EN_US);
        final Map<String, String> loggingParams = loggingParamsBuilder.build();

        assertEquals(VALUE, loggingParams.get(KEY));
        assertEquals(RAVEN_CAMPAIGN_NAME_EN_US, loggingParams.get(LogEntryKeys.RAVEN_CAMPAIGN_NAME));
        assertEquals(Status.SENT.toString(), loggingParams.get(LogEntryKeys.RAVEN_DELIVERY_STATUS_KEY));
        assertEquals(SESSION_UID, loggingParams.get(LogEntryKeys.RAVEN_TRACKING_KEY));
    }

    @Test
    public void testAddRavenInfoToLoggingParamMap_missingFields() {
        final ImmutableMap.Builder<String, String> loggingParamsBuilder = ImmutableMap.builder();
        final RavenEmail ravenEmail = RavenEmail.builder().build();

        handler.addRavenInfoToLoggingParamMap(loggingParamsBuilder, ravenEmail, RAVEN_CAMPAIGN_NAME_EN_US);
        final Map<String, String> loggingParams = loggingParamsBuilder.build();

        assertNull(loggingParams.get(KEY));
        assertNull(loggingParams.get(LogEntryKeys.RAVEN_DELIVERY_STATUS_KEY));
        assertNull(loggingParams.get(LogEntryKeys.RAVEN_TRACKING_KEY));
        assertEquals(RAVEN_CAMPAIGN_NAME_EN_US, loggingParams.get(LogEntryKeys.RAVEN_CAMPAIGN_NAME));
    }

    @Test
    public void testGetFormattedDate() {
        assertEquals("********", handler.getFormattedDate(MILLIS));
    }

    @Test
    public void testHandleEventClose() {
        handler.close();
    }

    @Test
    public void testGetRegisteredActivityTypes() {
        final List<ActivityType> expectResult =
                ImmutableList.of(ActivityType.PAUSE_JOB, ActivityType.SEND_END_DATE_REMINDER);
        final Collection<ActivityType> result = handler.getRegisteredActivityTypes();

        assertEquals(expectResult, result);
    }

    @Test
    public void GIVEN_jobHasEmptyLocations_WHEN_handleEvent_THEN_nackAndEmitInvalidJob() throws Exception {
        // GIVEN
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.CAD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(FRENCH_LOCALE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of())
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);

        // WHEN
        final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);

        // THEN
        assertEquals(ConsumerResponse.NACK, response);
        verify(metricsEmitter).emitEndDateEmailFailed(ErrorCause.INVALID_JOB);
    }

    @Test
    public void GIVEN_jobHasEndDate_WHEN_handleEvent_THEN_ack() throws Exception {
        // GIVEN
        final Advertiser advertiser = Advertiser.newBuilder()
                .setAdvertiserId(ADVERTISER_ID)
                .setCompany(COMPANY)
                .setCurrency(AdvertiserServiceProtos.CurrencyCode.USD)
                .setAccountIdLong(ACCOUNT_ID)
                .build();
        final List<AdvertiserProperty> advertiserProperties = ImmutableList.of(
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.SHOW_HOSTED_JOBS)
                        .setPropertyValue("notRejected")
                        .build(),
                AdvertiserProperty.newBuilder()
                        .setPropertyName(PropertyName.LOCALE)
                        .setPropertyValue(EN_LANGUAGE)
                        .build());

        when(advertiserUtil.getAdvertiserFromAdvertiserIdUnsafe(ADVERTISER_ID)).thenReturn(Optional.of(advertiser));
        when(advertiserUtil.getAdvertiserPropertiesUnsafe(
                        ADVERTISER_ID, ImmutableList.of(PropertyName.LOCALE, PropertyName.SHOW_HOSTED_JOBS)))
                .thenReturn(advertiserProperties);
        when(advertiserUtil.getBillingCountryForAdvertiserId(ADVERTISER_ID)).thenReturn(MAYBE_US_COUNTRY);

        final EndDateEmailJobData job = EndDateEmailJobData.builder()
                .company(COMPANY)
                .title(JOB_TITLE)
                .advertiserId(ADVERTISER_ID)
                .atsJobId(ATS_JOB_ID)
                .freeJob(false)
                .advertisingLocations(ImmutableList.of(LOCATION))
                .endDate(new java.util.Date(1234567890L))
                .build();
        when(hostedSponsoredJobsService.getJob(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(job);

        when(jobUtil.getCandidateCountsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID)).thenReturn(Optional.of(5));
        when(jobUtil.getTrafficStatsForJobUnsafe(ADVERTISER_ID, ATS_JOB_ID))
                .thenReturn(Optional.of(DailyTrafficStats.getDefaultInstance()));
        when(wallClock.currentTimeMillis()).thenReturn(MILLIS);

        final Map<String, Object> expectedDataMap = ImmutableMap.<String, Object>builder()
                .put(EndDateEmailHandler.JOB_TITLE_KEY, JOB_TITLE)
                .put(EndDateEmailHandler.JOB_LOCATION_KEY, LOCATION)
                .put(EndDateEmailHandler.COMPANY_KEY, COMPANY)
                .put(EndDateEmailHandler.CANDIDATE_COUNT_KEY, 5)
                .put(EndDateEmailHandler.NON_SPONSORED_JOB_APPS_KEY, 3)
                .put(EndDateEmailHandler.END_DATE_KEY, "March 27")
                .put(EndDateEmailHandler.DISPLAY_TOTAL_COST_KEY, "$0.00")
                .put(EndDateEmailHandler.RAVEN_DAYS_EARLY_KEY, ZERO_DAYS_EARLY_ZERO)
                .put(EndDateEmailHandler.ENCRYPTED_JOB_ID_KEY, ENCRYPTED_JOB_ID)
                .put(EndDateEmailHandler.RAVEN_DEDUPLICATION_ID, RAVEN_DEDUPE)
                .put(EndDateEmailHandler.JOB_HASH, JOB_HASH)
                .build();
        final RavenEmail ravenEmail = RavenEmail.builder()
                .ravenSessionUid(SESSION_UID)
                .status(Status.SENT)
                .advertiserId(ADVERTISER_ID)
                .dataMap(expectedDataMap)
                .campaignName(RAVEN_CAMPAIGN_NAME_EN_US)
                .build();
        when(emailUtil.sendEmailUnsafe(ADVERTISER_ID, RAVEN_CAMPAIGN_NAME_EN_US, expectedDataMap, Locale.US))
                .thenReturn(Optional.of(ravenEmail));

        try (final MockedStatic<EncryptionUtil> mockedEncryptionUtil = mockStatic(EncryptionUtil.class);
                final MockedStatic<DradisJobUtils> mockedDradisJobUtils = mockStatic(DradisJobUtils.class);
                final MockedStatic<SuperSmartDateFormatter> mockedSuperSmartDateFormatter =
                        mockStatic(SuperSmartDateFormatter.class)) {

            mockedEncryptionUtil
                    .when(() ->
                            EncryptionUtil.safeEncode(ATS_JOB_ID, ADVERTISER_ID, EncryptionUtil.EncryptionType.JOB_ID))
                    .thenReturn(ENCRYPTED_JOB_ID);
            mockedDradisJobUtils
                    .when(() -> DradisJobUtils.getDisplayLocationStringFromStrings(
                            ImmutableList.of(LOCATION), Locale.US.getCountry(), Locale.US, false))
                    .thenReturn(LOCATION);

            mockedSuperSmartDateFormatter
                    .when(() -> SuperSmartDateFormatter.formatDate(anyLong(), anyLong(), any(), any()))
                    .thenReturn("March 27");

            // WHEN
            final ConsumerResponse response = handler.handleEvent(PAUSE_EVENT);

            // THEN
            assertEquals(ConsumerResponse.ACK, response);
        }
    }
}
