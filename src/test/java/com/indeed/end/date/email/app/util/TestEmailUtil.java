package com.indeed.end.date.email.app.util;

import com.google.common.collect.ImmutableMap;
import com.indeed.end.date.email.app.metrics.MetricsEmitter;
import com.indeed.end.date.email.app.model.RavenEmail;
import com.indeed.logging.client.uid.UID;
import com.indeed.raven.delivery.client.DeliverCampaignSession;
import com.indeed.raven.delivery.client.RavenDeliveryClient;
import com.indeed.raven.delivery.client.RavenDeliveryClient.RecipientBuilder;
import com.indeed.raven.delivery.client.RavenDeliveryRpcClientException;
import com.indeed.raven.delivery.rpc.DeliverCampaignResponse;
import com.indeed.raven.delivery.rpc.RecipientResult;
import com.indeed.raven.delivery.rpc.Status;
import com.indeed.util.core.time.WallClock;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestEmailUtil {

    private static final int ADVERTISER_ID = 123;
    private static final String CAMPAIGN_NAME = "testCampaign";
    private static final Map<String, Object> DATA_MAP = ImmutableMap.of("key", 1);
    private static final String LANGUAGE = "fr";
    private static final String COUNTRY = "FR";
    private static final Locale ADVERTISER_LOCALE = new Locale(LANGUAGE, COUNTRY);

    @Mock
    private RavenDeliveryClient ravenDeliveryClient;

    @Mock
    private DeliverCampaignSession deliverCampaignSession;

    @Mock
    private RecipientBuilder recipientBuilder;

    @Mock
    private UID uid;

    @Mock
    private MetricsEmitter metricsEmitter;

    @Mock
    private WallClock wallClock;

    @InjectMocks
    private EmailUtil emailUtil;

    @Test
    public void testSendEmailUnsafe() throws RavenDeliveryRpcClientException {
        when(ravenDeliveryClient.startCampaign(CAMPAIGN_NAME)).thenReturn(deliverCampaignSession);
        when(deliverCampaignSession.newBuilder()).thenReturn(recipientBuilder);
        when(recipientBuilder.withRecipientId(ADVERTISER_ID)).thenReturn(recipientBuilder);
        when(recipientBuilder.withDataMap(DATA_MAP)).thenReturn(recipientBuilder);
        when(recipientBuilder.withLanguage(LANGUAGE)).thenReturn(recipientBuilder);
        when(recipientBuilder.withCountry(COUNTRY)).thenReturn(recipientBuilder);

        when(deliverCampaignSession.getCampaignUid()).thenReturn(uid);

        final DeliverCampaignResponse response = DeliverCampaignResponse.newBuilder()
                .addRecipientResult(RecipientResult.newBuilder()
                        .setRecipientId(ADVERTISER_ID)
                        .setStatus(Status.SENT))
                .build();
        when(deliverCampaignSession.send()).thenReturn(response);

        final Optional<RavenEmail> result =
                emailUtil.sendEmailUnsafe(ADVERTISER_ID, CAMPAIGN_NAME, DATA_MAP, ADVERTISER_LOCALE);

        assertTrue(result.isPresent());

        final RavenEmail ravenEmail = result.get();
        assertEquals(CAMPAIGN_NAME, ravenEmail.getCampaignName());
        assertEquals(ADVERTISER_ID, ravenEmail.getAdvertiserId());
        assertEquals(DATA_MAP, ravenEmail.getDataMap());
        assertEquals(uid.toString(), ravenEmail.getRavenSessionUid());
        assertEquals(Status.SENT, ravenEmail.getStatus());

        verify(recipientBuilder, times(1)).build();
    }

    @Test
    public void testSendEmailUnsafe_invalidAdvertiserId() throws RavenDeliveryRpcClientException {
        assertEquals(Optional.empty(), emailUtil.sendEmailUnsafe(0, CAMPAIGN_NAME, DATA_MAP, ADVERTISER_LOCALE));
    }

    @Test
    public void testSendEmailUnsafe_invalidCampaignName() throws RavenDeliveryRpcClientException {
        assertEquals(Optional.empty(), emailUtil.sendEmailUnsafe(ADVERTISER_ID, "", DATA_MAP, ADVERTISER_LOCALE));
        assertEquals(Optional.empty(), emailUtil.sendEmailUnsafe(ADVERTISER_ID, null, DATA_MAP, ADVERTISER_LOCALE));
    }

    @Test
    public void testSendEmailUnsafe_statusMissing() throws RavenDeliveryRpcClientException {
        when(ravenDeliveryClient.startCampaign(CAMPAIGN_NAME)).thenReturn(deliverCampaignSession);
        when(deliverCampaignSession.newBuilder()).thenReturn(recipientBuilder);
        when(recipientBuilder.withRecipientId(ADVERTISER_ID)).thenReturn(recipientBuilder);
        when(recipientBuilder.withDataMap(DATA_MAP)).thenReturn(recipientBuilder);
        when(recipientBuilder.withLanguage(LANGUAGE)).thenReturn(recipientBuilder);
        when(recipientBuilder.withCountry(COUNTRY)).thenReturn(recipientBuilder);

        when(deliverCampaignSession.getCampaignUid()).thenReturn(uid);
        when(deliverCampaignSession.send()).thenReturn(DeliverCampaignResponse.getDefaultInstance());

        final Optional<RavenEmail> result =
                emailUtil.sendEmailUnsafe(ADVERTISER_ID, CAMPAIGN_NAME, DATA_MAP, ADVERTISER_LOCALE);

        assertTrue(result.isPresent());

        final RavenEmail ravenEmail = result.get();
        assertEquals(CAMPAIGN_NAME, ravenEmail.getCampaignName());
        assertEquals(ADVERTISER_ID, ravenEmail.getAdvertiserId());
        assertEquals(DATA_MAP, ravenEmail.getDataMap());
        assertEquals(uid.toString(), ravenEmail.getRavenSessionUid());
        assertNull(ravenEmail.getStatus());

        verify(recipientBuilder, times(1)).build();
    }
}
