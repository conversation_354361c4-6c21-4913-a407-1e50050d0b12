specVersion: 1
entrypoints:
  # This template does not require any setup. Update this entrypoint if you make changes that require setup.
  setup:
    command: echo "No setup needed."
  build:
    command: ./gradlew assemble
  unitTest:
    command: ./gradlew check
  # This template does not come with integration tests. Update this entrypoint when you add integration tests.
  integrationTest:
    command: echo "No integration tests. Update this entrypoint when you add integration tests."
  updateDependencies:
    command: ./gradlew lockdown
  run:
    command: ./gradlew run
