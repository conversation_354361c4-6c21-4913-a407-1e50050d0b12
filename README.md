Running the project locally
----
# How to run locally
1. Copy application-env-local.yml.examples to application-env-local.yml and set up configuration.
   Note: The adcentral-aws-rabbit-consumer-config.properties can be pulled from vault for the required property.
2. You need to start Envoy first.
```bash
envoy-qa-start.sh -d <PERSON><PERSON><PERSON>,advertiserservice,loyal-service,raven-delivery,one-graph,advertisementservice,reporting-service -n end-date-email-app
```

3. Start job:
```bash
MESH_HOST_AND_PORT=localhost:4444 ./gradlew run
```
example: MESH_HOST_AND_PORT=localhost:4444 ./gradlew run

See the [wiki]() for more information
