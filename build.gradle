plugins {
    id 'com.indeed.common'
    id 'com.indeed.package'
    id 'com.indeed.springboot'
    id 'com.indeed.proctor'
    id 'com.indeed.i18n'
    id 'com.apollographql.apollo3' version "${apolloVersion}"
}

dependencies {
    // Spring Boot Web starter for Spring MVC
    compile 'org.springframework.boot:spring-boot-starter-web'

    compile 'com.indeed:indeed-spring-boot-starter'
    compile 'com.indeed:poodlepants-spring-boot-starter'
    compile 'indeed:common-message'
    compile('indeed:adcentral-proto') {
        exclude group: 'org.elasticsearch', module: 'elasticsearch'
    }
    compile 'indeed:advertiserservice'
    compile 'indeed:raven-delivery-client'
    compile 'com.indeed:monetization-platform-library-core-spring-boot-starter'
    compile 'indeed:cally'
    compile 'com.indeed:indeed-apollo-onegraph-client-spring-boot-starter'
    compile 'indeed:common-i18n-spring'
    compile 'indeed:mopsy'
    compile 'indeed:common-message-rabbit'

    compile "org.checkerframework:checker-qual"
    // proctor
    compile 'indeed:proctor-internal'
    compile 'jakarta.servlet:jakarta.servlet-api:5.0.0'
    // jsr305: Annotation for software defect detection
    compileOnly 'com.google.code.findbugs:jsr305'
    testCompileOnly 'com.google.code.findbugs:jsr305'
    // Lombok
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'
    compile 'org.jooq:jooq:3.6.0'
    //GraphQL
    compile "com.apollographql.apollo3:apollo-runtime:${apolloVersion}"
    compile "com.apollographql.apollo3:apollo-rx3-support:${apolloVersion}"

    // Test Only
    testCompile 'org.mockito:mockito-core'
    testCompile 'com.indeed:indeed-spring-boot-starter-test'

    testCompile 'junit:junit:4.13.2'
}

indeedI18n {
    javaBaseName = 'com.indeed.loyal.daemon.i18n.Messages'
    extractLocales = 'ar cs da de el en es fi fr hu in it ja ko ms nl no pl pt ro ru sv th tr uk vi zh zz'
    extractTypes = 'java jsp'
    // jsEnabled = true // optional - Enables javascript i18n file generation
    // jsClasspathDirectory = '' // optional - Specifies where on the classpath the i18n javascript files will be placed
    // javaUseArtifactParent = true // optional -- set this if you use translations-as-an-artifact
    // parseJsDir = 'src/main/npm' // optional -- specifies the directory that contains javascript files to parse from, default to src/main/npm, see gettext_extract_strings.sh (PARSEJS_DIR) in javadev
    // parseTsDir = 'src/main/npm' // optional -- specifies the directory that contains typescript files to parse from, default to src/main/npm, see gettext_extract_strings.sh (PARSETS_DIR) in javadev
    // nodeModulePo = false // optional -- import po files from npm packages, default to false
}

indeedProctor {
    add {
        outputPackageName = 'com.indeed.end.date.email.app.groups'
        outputSpecPath = 'proctor/end-date-email-app-proctor-specification.json'
        outputGroupsClass = 'EndDateEmailAppProctorGroups'
        outputGroupsManagerClass = 'EndDateEmailAppGroupsManager'
        outputContextClass = 'EndDateEmailAppProctorContext'
    }
}

run {
    mainClass = 'com.indeed.end.date.email.app.EndDateEmailApp'
}

apollo {
    // Use "./gradlew downloadOneGraphApolloSchema" to execute this
    service('oneGraph') {
        schemaFile.set(file('src/main/graphql/com/indeed/one/graph/client/schema.graphqls'))
        srcDir('src/main/graphql/com/indeed/one/graph/client')
        packageName.set('com.indeed.one.graph.client')
        introspection {
            endpointUrl = "https://apis.qa.indeed.tech/graphql"
            endpointUrl = 'https://apis.qa.indeed.tech/graphql'
        }
    }
}

apply from: 'jacocoConfig.gradle'