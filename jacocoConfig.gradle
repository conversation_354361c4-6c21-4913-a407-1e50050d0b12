apply plugin: 'jacoco'
// Ref: https://docs.gradle.org/current/userguide/jacoco_plugin.html

// Exclude the following patterns from coverage analysis (both reports and enforcement):
def jacocoExcludes = [

        // DTOs/data models with no business logic
        "com/indeed/end/date/email/app/model/**",

        // Logging utilities/wrappers
        "com/indeed/end/date/email/app/logging/**",

        // Custom exceptions
        "com/indeed/end/date/email/app/exception/**",

        // Client classes
        "com/indeed/end/date/email/app/client/**",

        // Configuration classes
        "com/indeed/end/date/email/app/config/**",

        // OneGraph client configuration
        "com/indeed/end/date/email/app/onegraph/config/**",

        // Daemon wiring/config
        "com/indeed/end/date/email/app/adceventsdaemon/config/**",

        // Static constants holder
        "com/indeed/end/date/email/app/util/Constants.class",

        // Spring Boot entrypoint
        "com/indeed/end/date/email/app/EndDateEmailApp.class",

        // i18n resource helpers/generated classes
        "com/indeed/loyal/daemon/i18n/**",

        // generated by Proctor
        "com/indeed/end/date/email/app/groups/**",

        // Generated by Apollo
        "com/indeed/one/graph/client/**",
]

def jacocoClasses = jacocoTestReport.classDirectories.files.collect {
    fileTree(dir: it, excludes: jacocoExcludes)
}

jacocoTestReport {
    reports {
        xml.destination file("${buildDir}/coverage/coverage_jacoco.xml")
    }
    afterEvaluate {
        classDirectories.setFrom(jacocoClasses)
    }
}

jacocoTestCoverageVerification {
    afterEvaluate {
        classDirectories.setFrom(jacocoClasses)
    }
    violationRules {
        rule {
            limit {
                counter = 'INSTRUCTION'  // default counter type, better than counting lines
                minimum = 1.00 // Minimum total coverage ratio of instructions
            }
            limit {
                counter = 'BRANCH'
                minimum = 1.00 // Minimum total coverage ratio of branches
            }
        }
    }
}

check {
    dependsOn jacocoTestReport
    dependsOn jacocoTestCoverageVerification
}
