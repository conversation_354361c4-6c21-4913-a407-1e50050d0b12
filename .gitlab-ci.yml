include:
  - project: pipeline/gitlab-pipeline
    file: /templates/java-golden.yml

variables:
  HAS_DEPLOYABLE: "true"
  LEMMA: "false" # TODO: Edit config files in lemma/ and then set this to "true" to use LEMMA
  UPLOAD_TO_ARC_WITHOUT_LEMMA: "true"

  QA_DEPLOY_URL: "https://end-date-email-app.sandbox.qa.indeed.net" # TODO: insert your QA URL here
  PROD_DEPLOY_URL: "https://end-date-email-app.sandbox.indeed.net" # TODO: insert your PROD URL here
  DEPLOYMENT_GROUP: "primary"

  # Push On Green is enabled by default. To disable it, set PUSH_ON_GREEN to false

  # QA and Prod deployments are enabled by default. Make sure to configure your Marvin deployment groups with
  # correct deploy labels (QA uses qa-jenkins-latest and Prod uses prod-jenkins-latest) before triggering a pipeline
  # run in the main branch

  # Canary deployment is optional. To use it, configure a canary deployment group on <PERSON> and set HAS_CANARY to true

  # ARC_NAMESPACE and MARVIN_NAMESPACE is set to be the same as Gitlab repo name by default.
  # If you have a different marvin project name, please update PRODUCT_GROUP
