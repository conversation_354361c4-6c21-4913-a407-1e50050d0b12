type Query {
  employerJob(id: ID!): EmployerJob
  campaigns(
    first: Int
    after: String
    status: CampaignStatus
    inCampaignApiMigration: Boolean = false
  ): CampaignConnection!
  findHostedJobPosts(input: FindHostedJobPostsInput): FindHostedJobPostsPayload
}

type EmployerJob {
  id: ID!
  title: String!
  description: String
  location: Location
  salary: Salary
  campaignsConnection(
    first: Int
    after: String
    status: CampaignStatus
    input: EmployerJobCampaignsConnectionInput
  ): CampaignConnection!
  activeCampaign: Campaign
  hostedJobBudget: HostedJobBudget @deprecated(reason: "Use campaignsConnection instead. Will be removed in future versions.")
}

type HostedJobBudget {
  isFreeToPost: Boolean!
  period: BudgetPeriod
  amount: Float
  currency: String
  endDate: DateTime
  upid: String
}

enum BudgetPeriod {
  DAILY
  MONTHLY
}

type CampaignConnection {
  edges: [CampaignEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type CampaignEdge {
  node: Campaign!
  cursor: String!
}

type Campaign {
  id: ID!
  status: CampaignStatus!
  budget: CampaignBudget!
  spent: Money
  startDate: DateTime
  endDate: DateTime
  metrics: CampaignMetrics
  employerJob: EmployerJob!
  packageInfo: PackageInfo
  schedule: CampaignSchedule
  experimental: ExperimentalCampaignData
  upid: String
}

type CampaignBudget {
  limit: Money!
  recurringUnit: RecurringUnit
}

type PackageInfo {
  packageId: String!
  name: String
  description: String
}

type CampaignSchedule {
  extStartDateTime: DateTime
  extEndDateTime: DateTime
  actualStartDateTime: DateTime
  actualEndDateTime: DateTime
  endDateTime: DateTime
  targetEndDateTime: DateTime
}

type ExperimentalCampaignData {
  payPerDay: Money
  recurringUnit: RecurringUnit
}

enum RecurringUnit {
  DAILY
  WEEKLY
  MONTHLY
}

type CampaignMetrics {
  impressions: Int!
  clicks: Int!
  applications: Int!
  costPerClick: Money
  costPerApplication: Money
}

enum CampaignStatus {
  DRAFT
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

type Money {
  amount: Float!
  currency: String!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

type Location {
  city: String
  state: String
  country: String
  remote: Boolean
}

type Salary {
  min: Float
  max: Float
  currency: String
  period: SalaryPeriod
}

enum SalaryPeriod {
  HOURLY
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}

type DateTime {
  iso8601: String!
}

# Mutations for Campaign management
type Mutation {
  createCampaign(input: CreateCampaignInput!): CreateCampaignPayload!
  updateCampaign(input: UpdateCampaignInput!): UpdateCampaignPayload!
  pauseCampaign(id: ID!): PauseCampaignPayload!
  resumeCampaign(id: ID!): ResumeCampaignPayload!
  cancelCampaign(id: ID!): CancelCampaignPayload!
}

input CreateCampaignInput {
  employerJobId: ID!
  budget: MoneyInput!
  recurringUnit: RecurringUnit
  startDate: String
  endDate: String
}

input UpdateCampaignInput {
  id: ID!
  budget: MoneyInput
  startDate: String
  endDate: String
}

input MoneyInput {
  amount: Float!
  currency: String!
}

type CreateCampaignPayload {
  campaign: Campaign
  errors: [Error!]
}

type UpdateCampaignPayload {
  campaign: Campaign
  errors: [Error!]
}

type PauseCampaignPayload {
  campaign: Campaign
  errors: [Error!]
}

type ResumeCampaignPayload {
  campaign: Campaign
  errors: [Error!]
}

type CancelCampaignPayload {
  campaign: Campaign
  errors: [Error!]
}

type Error {
  message: String!
  code: String!
}

# Input types for Campaign Connection
input EmployerJobCampaignsConnectionInput {
  filter: EmployerJobCampaignsConnectionFilterInput
  lookupStrategies: [JobToCampaignLookupStrategy!]
}

input EmployerJobCampaignsConnectionFilterInput {
  isHosted: Boolean
  extRefTypes: [CampaignExtRefType!]
}

enum JobToCampaignLookupStrategy {
  JOB_GROUP
  DIRECT_ASSOCIATION
}

enum CampaignExtRefType {
  SMB_SINGLE_JOB
  ENTERPRISE_CAMPAIGN
}

# Input for finding hosted job posts
input FindHostedJobPostsInput {
  filter: FindHostedJobPostsFilterInput
}

input FindHostedJobPostsFilterInput {
  hostedJobIds: [String!]
}

# Campaign update mutation
input UpdateCampaignWithJobsInput {
  rawId: String
  id: String
  status: CampaignStatus
  budgetInput: UpdateCampaignBudgetInput
  experimentalInput: UpdateCampaignExperimentalPropertiesInput
  idempotencyKey: String!
}

input UpdateCampaignBudgetInput {
  limit: MonetaryValueInput!
  recurringUnit: RecurringUnit
}

input MonetaryValueInput {
  amountLocal: Float!
  currency: String!
}

input UpdateCampaignExperimentalPropertiesInput {
  payPerDayInput: String
}

type UpdateCampaignWithJobsMutation {
  success: Boolean!
  campaign: Campaign
  errors: [Error!]
}

type FindHostedJobPostsPayload {
  nodes: [FindHostedJobPostsNode!]
  errors: [Error!]
}

type FindHostedJobPostsNode {
  id: ID!
  status: CampaignStatus!
  budget: CampaignBudget
  employerJob: EmployerJob
}
